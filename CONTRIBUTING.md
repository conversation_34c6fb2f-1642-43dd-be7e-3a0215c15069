# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON>! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git
- Basic understanding of TypeScript, WebSockets, and MCP protocol

### Development Setup

1. **Fork and clone the repository**:
   ```bash
   git clone https://github.com/your-username/powersteer.git
   cd powersteer
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the project**:
   ```bash
   npm run build
   ```

4. **Run tests**:
   ```bash
   npm test
   ```

5. **Start development servers**:
   ```bash
   # Terminal 1: Start server
   npm run dev:server
   
   # Terminal 2: Start CLI
   npm run dev:cli
   ```

### Project Structure

```
powersteer/
├── packages/
│   ├── server/          # MCP server implementation
│   ├── cli/             # Interactive CLI application
│   └── common/          # Shared types and utilities
├── docs/                # Documentation
├── tests/               # Integration tests
├── config/              # Configuration files
└── scripts/             # Build and utility scripts
```

## 📝 Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

- **Bug fixes**: Fix issues and improve stability
- **Features**: Add new functionality
- **Documentation**: Improve guides, API docs, examples
- **Tests**: Add test coverage and improve reliability
- **Performance**: Optimize code and reduce resource usage
- **Refactoring**: Improve code quality and maintainability

### Before You Start

1. **Check existing issues**: Look for related issues or discussions
2. **Create an issue**: For significant changes, create an issue first to discuss
3. **Assign yourself**: Comment on the issue to indicate you're working on it
4. **Follow conventions**: Adhere to our coding standards and patterns

## 🔧 Development Workflow

### Branch Naming

Use descriptive branch names with prefixes:

- `feature/add-session-persistence`
- `fix/websocket-connection-leak`
- `docs/api-documentation-update`
- `refactor/error-handling-cleanup`
- `test/integration-test-coverage`

### Commit Messages

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples**:
```
feat(server): add session persistence with Redis

fix(cli): resolve WebSocket reconnection issue

docs(api): update authentication examples

test(server): add error recovery test coverage
```

### Pull Request Process

1. **Create feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make changes**:
   - Write code following our style guidelines
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**:
   ```bash
   npm run test
   npm run lint
   npm run build
   ```

4. **Commit changes**:
   ```bash
   git add .
   git commit -m "feat(scope): your descriptive message"
   ```

5. **Push and create PR**:
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create Pull Request**:
   - Use the PR template
   - Provide clear description
   - Link related issues
   - Request review from maintainers

### Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)

## Related Issues
Fixes #(issue number)
```

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── unit/                # Unit tests for individual components
├── integration/         # Integration tests for component interaction
├── e2e/                 # End-to-end tests for complete workflows
└── fixtures/            # Test data and mock objects
```

### Writing Tests

1. **Unit Tests**: Test individual functions and classes
   ```typescript
   describe('SessionManager', () => {
     it('should create new session with unique ID', () => {
       const manager = new SessionManager();
       const session = manager.createSession();
       expect(session.id).toBeDefined();
       expect(session.id).toMatch(/^session_[a-f0-9]+$/);
     });
   });
   ```

2. **Integration Tests**: Test component interactions
   ```typescript
   describe('WebSocket Server Integration', () => {
     it('should handle client connection and message exchange', async () => {
       const server = new PowersteerServer();
       const client = new WebSocket('ws://localhost:3001');
       
       await new Promise(resolve => client.on('open', resolve));
       
       client.send(JSON.stringify({
         type: 'agent_request',
         content: 'Test message'
       }));
       
       const response = await new Promise(resolve => 
         client.on('message', resolve)
       );
       
       expect(JSON.parse(response)).toMatchObject({
         type: 'system',
         content: expect.stringContaining('received')
       });
     });
   });
   ```

3. **Error Recovery Tests**: Test failure scenarios
   ```typescript
   describe('Error Recovery', () => {
     it('should reconnect after connection failure', async () => {
       const client = new PowersteerClient();
       const reconnectSpy = jest.spyOn(client, 'reconnect');
       
       // Simulate connection failure
       client.ws.emit('error', new Error('Connection failed'));
       
       await new Promise(resolve => setTimeout(resolve, 1000));
       
       expect(reconnectSpy).toHaveBeenCalled();
     });
   });
   ```

### Test Coverage

Maintain high test coverage:
- **Unit tests**: >90% coverage
- **Integration tests**: Cover all major workflows
- **Error scenarios**: Test failure modes and recovery

```bash
# Check coverage
npm run test:coverage

# Coverage report
open coverage/lcov-report/index.html
```

## 📋 Code Style Guidelines

### TypeScript Standards

1. **Type Safety**: Use strict TypeScript configuration
   ```typescript
   // Good
   interface User {
     id: string;
     email: string;
     createdAt: Date;
   }
   
   function createUser(data: Omit<User, 'id' | 'createdAt'>): User {
     return {
       id: generateId(),
       createdAt: new Date(),
       ...data
     };
   }
   
   // Avoid
   function createUser(data: any): any {
     return { id: Math.random(), ...data };
   }
   ```

2. **Error Handling**: Use proper error types
   ```typescript
   // Good
   class ValidationError extends Error {
     constructor(field: string, value: unknown) {
       super(`Invalid value for field ${field}: ${value}`);
       this.name = 'ValidationError';
     }
   }
   
   // Avoid
   throw new Error('Something went wrong');
   ```

3. **Async/Await**: Prefer async/await over promises
   ```typescript
   // Good
   async function processRequest(request: AgentRequest): Promise<HumanResponse> {
     try {
       const validation = await validateRequest(request);
       const response = await getHumanInput(validation);
       return response;
     } catch (error) {
       throw new ProcessingError('Failed to process request', error);
     }
   }
   
   // Avoid
   function processRequest(request: AgentRequest): Promise<HumanResponse> {
     return validateRequest(request)
       .then(validation => getHumanInput(validation))
       .catch(error => { throw error; });
   }
   ```

### Code Organization

1. **Single Responsibility**: Each module should have one clear purpose
2. **Dependency Injection**: Use dependency injection for testability
3. **Error Boundaries**: Implement proper error boundaries
4. **Resource Cleanup**: Always clean up resources (connections, timers, etc.)

### Linting and Formatting

```bash
# Run linter
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Check formatting
npm run format:check
```

### Configuration Files

**.eslintrc.js**:
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'prettier'
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

## 📚 Documentation Standards

### Code Documentation

1. **JSDoc Comments**: Document public APIs
   ```typescript
   /**
    * Creates a new session for agent-human communication
    * @param agentId - Unique identifier for the requesting agent
    * @param options - Optional session configuration
    * @returns Promise resolving to session information
    * @throws {ValidationError} When agentId is invalid
    * @example
    * ```typescript
    * const session = await createSession('agent-123', {
    *   timeout: 300000
    * });
    * ```
    */
   async function createSession(
     agentId: string, 
     options?: SessionOptions
   ): Promise<Session> {
     // Implementation
   }
   ```

2. **README Updates**: Update relevant README files
3. **API Documentation**: Update API docs for interface changes
4. **Examples**: Provide usage examples for new features

### Documentation Structure

- **README.md**: Project overview and quick start
- **docs/API.md**: Comprehensive API documentation
- **docs/DEPLOYMENT.md**: Production deployment guide
- **docs/TROUBLESHOOTING.md**: Common issues and solutions
- **CONTRIBUTING.md**: This file - contribution guidelines

## 🔍 Review Process

### Code Review Checklist

**Functionality**:
- [ ] Code works as intended
- [ ] Edge cases handled
- [ ] Error conditions managed
- [ ] Performance considerations addressed

**Code Quality**:
- [ ] Follows project conventions
- [ ] Proper error handling
- [ ] Resource cleanup
- [ ] Type safety maintained

**Testing**:
- [ ] Adequate test coverage
- [ ] Tests pass consistently
- [ ] Error scenarios tested
- [ ] Integration tests included

**Documentation**:
- [ ] Code documented appropriately
- [ ] API changes documented
- [ ] Examples provided
- [ ] README updated if needed

### Review Timeline

- **Initial review**: Within 2-3 business days
- **Follow-up reviews**: Within 1 business day
- **Merge**: After approval from at least one maintainer

## 🏆 Recognition

Contributors are recognized in:
- **CONTRIBUTORS.md**: List of all contributors
- **Release notes**: Major contributions highlighted
- **GitHub**: Contributor badges and statistics

## 📞 Getting Help

- **GitHub Discussions**: Ask questions and discuss ideas
- **Issues**: Report bugs or request features
- **Discord**: Real-time chat with maintainers and community
- **Email**: <EMAIL> for sensitive issues

## 📄 License

By contributing to Powersteer, you agree that your contributions will be licensed under the same license as the project (MIT License).

Thank you for contributing to Powersteer! 🚀
