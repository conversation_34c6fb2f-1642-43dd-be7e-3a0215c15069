systemPrompt: |
  Always use SOLID and DRY principles when writing code.
  Focus on maintainable, testable, and scalable solutions.
  Prefer composition over inheritance.
  Write comprehensive tests for all functionality.

userPrompt: |
  Please provide detailed explanations for your decisions.
  Consider edge cases and error handling.
  Follow the established code patterns in the project.

# Optional server configuration
serverUrl: ws://localhost:8080
reconnectInterval: 3000
maxReconnectAttempts: 10
