#!/usr/bin/env node

/**
 * End-to-End Integration Test for Powersteer
 * 
 * This test validates the complete system integration:
 * 1. Server startup and initialization
 * 2. CLI connection to server
 * 3. Agent request simulation
 * 4. Human response handling
 * 5. Complete message flow validation
 */

import { spawn } from 'child_process';
import WebSocket from 'ws';

const TEST_PORT = 8081; // Use different port for testing
const TEST_TIMEOUT = 30000; // 30 seconds

class IntegrationTest {
  constructor() {
    this.serverProcess = null;
    this.cliProcess = null;
    this.testResults = [];
  }

  log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`);
  }

  error(message) {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
  }

  async startServer() {
    this.log('Starting Powersteer Server...');
    
    return new Promise((resolve, reject) => {
      this.serverProcess = spawn('node', ['packages/server/build/cli.js'], {
        env: { ...process.env, POWERSTEER_PORT: TEST_PORT },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      this.serverProcess.stdout.on('data', (data) => {
        output += data.toString();
        if (output.includes('Powersteer Server is ready for connections')) {
          this.log('Server started successfully');
          resolve();
        }
      });

      this.serverProcess.stderr.on('data', (data) => {
        const stderrData = data.toString();
        output += stderrData; // Also check stderr for server ready message
        if (stderrData.includes('Powersteer Server is ready for connections')) {
          this.log('Server started successfully');
          resolve();
        }
        // Only log as error if it doesn't contain expected startup messages
        if (!stderrData.includes('Powersteer') && !stderrData.includes('✓') && !stderrData.includes('Configuration')) {
          this.error(`Server stderr: ${stderrData}`);
        }
      });

      this.serverProcess.on('error', (error) => {
        this.error(`Server process error: ${error.message}`);
        reject(error);
      });

      // Timeout after 10 seconds
      const timeoutId = setTimeout(() => {
        if (!output.includes('Powersteer Server is ready for connections')) {
          reject(new Error('Server startup timeout'));
        }
      }, 10000);
    });
  }

  async testWebSocketConnection() {
    this.log('Testing WebSocket connection...');
    
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(`ws://localhost:${TEST_PORT}`);
      
      ws.on('open', () => {
        this.log('WebSocket connection established');
        ws.close();
        resolve();
      });

      ws.on('error', (error) => {
        this.error(`WebSocket connection failed: ${error.message}`);
        reject(error);
      });

      setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 5000);
    });
  }

  async testMessageExchange() {
    this.log('Testing message exchange...');

    return new Promise((resolve, reject) => {
      const ws = new WebSocket(`ws://localhost:${TEST_PORT}`);
      let messageReceived = false;

      ws.on('open', () => {
        this.log('Sending test message...');

        const testMessage = {
          type: 'ping',
          id: 'test-ping-001',
          timestamp: new Date().toISOString(),
          data: 'Integration test ping'
        };

        ws.send(JSON.stringify(testMessage));

        // Close connection after sending message
        setTimeout(() => {
          this.log('Message exchange test completed');
          ws.close();
          resolve();
        }, 1000);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.log(`Received message: ${message.type}`);
          messageReceived = true;
        } catch (error) {
          this.error(`Message parsing error: ${error.message}`);
        }
      });

      ws.on('error', (error) => {
        this.error(`WebSocket error: ${error.message}`);
        reject(error);
      });

      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
        }
        reject(new Error('Message exchange timeout'));
      }, 5000);
    });
  }

  async testCLIConnection() {
    this.log('Testing CLI connection...');
    
    return new Promise((resolve, reject) => {
      this.cliProcess = spawn('node', ['packages/cli/build/cli.js', '--server-url', `ws://localhost:${TEST_PORT}`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let connected = false;

      this.cliProcess.stdout.on('data', (data) => {
        output += data.toString();
        if (output.includes('Connected') && !connected) {
          connected = true;
          this.log('CLI connected successfully');
          
          // Give it a moment to stabilize
          setTimeout(() => {
            this.cliProcess.kill('SIGTERM');
            resolve();
          }, 2000);
        }
      });

      this.cliProcess.stderr.on('data', (data) => {
        this.error(`CLI stderr: ${data}`);
      });

      this.cliProcess.on('error', (error) => {
        this.error(`CLI process error: ${error.message}`);
        reject(error);
      });

      setTimeout(() => {
        if (!connected) {
          this.cliProcess.kill('SIGTERM');
          reject(new Error('CLI connection timeout'));
        }
      }, 10000);
    });
  }

  async testSystemPerformance() {
    this.log('Testing system performance...');
    
    const startTime = Date.now();
    const connections = [];
    
    try {
      // Create multiple concurrent connections
      for (let i = 0; i < 5; i++) {
        const ws = new WebSocket(`ws://localhost:${TEST_PORT}`);
        connections.push(ws);
        
        await new Promise((resolve) => {
          ws.on('open', resolve);
          ws.on('error', resolve);
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.log(`Performance test completed in ${duration}ms`);
      
      // Clean up connections
      connections.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
        }
      });
      
      return duration < 5000; // Should complete within 5 seconds
    } catch (error) {
      this.error(`Performance test failed: ${error.message}`);
      return false;
    }
  }

  async cleanup() {
    this.log('Cleaning up test processes...');
    
    if (this.cliProcess) {
      this.cliProcess.kill('SIGTERM');
    }
    
    if (this.serverProcess) {
      this.serverProcess.kill('SIGTERM');
    }
    
    // Wait for processes to terminate
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  async runTests() {
    const startTime = Date.now();
    
    try {
      this.log('Starting Powersteer End-to-End Integration Tests');
      
      // Test 1: Server startup
      await this.startServer();
      this.testResults.push({ test: 'Server Startup', status: 'PASS' });
      
      // Test 2: WebSocket connection
      await this.testWebSocketConnection();
      this.testResults.push({ test: 'WebSocket Connection', status: 'PASS' });
      
      // Test 3: Message exchange
      await this.testMessageExchange();
      this.testResults.push({ test: 'Message Exchange', status: 'PASS' });
      
      // Test 4: CLI connection
      await this.testCLIConnection();
      this.testResults.push({ test: 'CLI Connection', status: 'PASS' });
      
      // Test 5: System performance
      const performanceResult = await this.testSystemPerformance();
      this.testResults.push({ 
        test: 'System Performance', 
        status: performanceResult ? 'PASS' : 'FAIL' 
      });
      
      const endTime = Date.now();
      const totalDuration = endTime - startTime;
      
      this.log('\n=== Integration Test Results ===');
      this.testResults.forEach(result => {
        this.log(`${result.test}: ${result.status}`);
      });
      
      const passCount = this.testResults.filter(r => r.status === 'PASS').length;
      const totalCount = this.testResults.length;
      
      this.log(`\nTotal: ${passCount}/${totalCount} tests passed`);
      this.log(`Duration: ${totalDuration}ms`);
      
      if (passCount === totalCount) {
        this.log('\n🎉 All integration tests PASSED!');
        process.exit(0);
      } else {
        this.error('\n❌ Some integration tests FAILED!');
        process.exit(1);
      }
      
    } catch (error) {
      this.error(`Integration test failed: ${error.message}`);
      this.testResults.push({ test: 'Overall', status: 'FAIL' });
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, cleaning up...');
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, cleaning up...');
  process.exit(1);
});

// Run the integration tests
const test = new IntegrationTest();
test.runTests().catch((error) => {
  console.error('Integration test runner failed:', error);
  process.exit(1);
});
