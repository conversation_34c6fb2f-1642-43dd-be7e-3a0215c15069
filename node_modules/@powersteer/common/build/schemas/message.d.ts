import { z } from 'zod';
/**
 * CLI message type enumeration
 */
export declare const CLIMessageTypeSchema: z.<PERSON>od<PERSON>num<["agent_request", "human_response", "system"]>;
/**
 * CLI message schema for display
 */
export declare const CLIMessageSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["agent_request", "human_response", "system"]>;
    content: z.ZodString;
    timestamp: z.ZodDate;
    sessionId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    type: "agent_request" | "human_response" | "system";
    content: string;
    timestamp: Date;
    sessionId?: string | undefined;
}, {
    id: string;
    type: "agent_request" | "human_response" | "system";
    content: string;
    timestamp: Date;
    sessionId?: string | undefined;
}>;
/**
 * TypeScript types derived from schemas
 */
export type CLIMessageType = z.infer<typeof CLIMessageTypeSchema>;
export type Message = z.infer<typeof CLIMessageSchema>;
//# sourceMappingURL=message.d.ts.map