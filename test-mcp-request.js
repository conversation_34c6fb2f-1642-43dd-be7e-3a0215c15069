#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Start the MCP server process
const mcpServer = spawn('node', ['packages/server/build/cli.js'], {
  cwd: __dirname,
  stdio: ['pipe', 'pipe', 'pipe']
});

// Send MCP request
const request = {
  jsonrpc: '2.0',
  id: 1,
  method: 'tools/call',
  params: {
    name: 'request_human_input',
    arguments: {
      agentId: 'test-agent',
      type: 'information',
      summary: 'Testing config prompts functionality - please respond with "test response"'
    }
  }
};

console.log('Sending MCP request:', JSON.stringify(request, null, 2));

// Send the request
mcpServer.stdin.write(JSON.stringify(request) + '\n');

// Handle responses
mcpServer.stdout.on('data', (data) => {
  const lines = data.toString().split('\n').filter(line => line.trim());
  for (const line of lines) {
    try {
      const response = JSON.parse(line);
      console.log('MCP Response:', JSON.stringify(response, null, 2));
    } catch (e) {
      console.log('Non-JSON output:', line);
    }
  }
});

mcpServer.stderr.on('data', (data) => {
  console.log('MCP Server stderr:', data.toString());
});

mcpServer.on('close', (code) => {
  console.log(`MCP Server process exited with code ${code}`);
});

// Keep the process alive for a while to wait for response
setTimeout(() => {
  console.log('Closing MCP server...');
  mcpServer.kill();
}, 30000);
