import { z } from 'zod';
/**
 * WebSocket message types
 */
export declare const messageTypeSchema: z.<PERSON><["connect", "disconnect", "agent_request", "human_response", "session_update", "error", "ping", "pong"]>;
/**
 * Base WebSocket message schema
 */
export declare const baseMessageSchema: z.ZodObject<{
    type: z.ZodEnum<["connect", "disconnect", "agent_request", "human_response", "session_update", "error", "ping", "pong"]>;
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "error" | "connect" | "disconnect" | "agent_request" | "human_response" | "session_update" | "ping" | "pong";
    timestamp: string;
    sessionId?: string | undefined;
}, {
    type: "error" | "connect" | "disconnect" | "agent_request" | "human_response" | "session_update" | "ping" | "pong";
    timestamp: string;
    sessionId?: string | undefined;
}>;
/**
 * Connection message schema
 */
export declare const connectMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"connect">;
    sessionId: z.ZodOptional<z.ZodString>;
    clientType: z.ZodEnum<["cli", "agent"]>;
}, "strip", z.ZodTypeAny, {
    type: "connect";
    timestamp: string;
    clientType: "cli" | "agent";
    sessionId?: string | undefined;
}, {
    type: "connect";
    timestamp: string;
    clientType: "cli" | "agent";
    sessionId?: string | undefined;
}>;
/**
 * Disconnect message schema
 */
export declare const disconnectMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"disconnect">;
    sessionId: z.ZodString;
    reason: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "disconnect";
    timestamp: string;
    reason?: string | undefined;
}, {
    sessionId: string;
    type: "disconnect";
    timestamp: string;
    reason?: string | undefined;
}>;
/**
 * Agent request message schema
 */
export declare const agentRequestMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"agent_request">;
    sessionId: z.ZodString;
    payload: z.ZodObject<{
        sessionId: z.ZodString;
        type: z.ZodEnum<["confirmation", "information", "decision", "approval", "feedback"]>;
        priority: z.ZodEnum<["low", "normal", "high", "urgent"]>;
        summary: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        timeout: z.ZodNumber;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "agent_request";
    timestamp: string;
    payload: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    };
}, {
    sessionId: string;
    type: "agent_request";
    timestamp: string;
    payload: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    };
}>;
/**
 * Human response message schema
 */
export declare const humanResponseMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"human_response">;
    sessionId: z.ZodString;
    payload: z.ZodObject<{
        sessionId: z.ZodString;
        response: z.ZodString;
        approved: z.ZodOptional<z.ZodBoolean>;
        data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        systemPrompt: z.ZodOptional<z.ZodString>;
        userPrompt: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }, {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "human_response";
    timestamp: string;
    payload: {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    };
}, {
    sessionId: string;
    type: "human_response";
    timestamp: string;
    payload: {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    };
}>;
/**
 * Session update message schema
 */
export declare const sessionUpdateMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"session_update">;
    sessionId: z.ZodString;
    status: z.ZodEnum<["active", "waiting", "completed", "timeout", "error"]>;
    message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "session_update";
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timestamp: string;
    message?: string | undefined;
}, {
    sessionId: string;
    type: "session_update";
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timestamp: string;
    message?: string | undefined;
}>;
/**
 * Error message schema
 */
export declare const errorMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"error">;
    error: z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodUnknown>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        details?: unknown;
    }, {
        code: string;
        message: string;
        details?: unknown;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "error";
    timestamp: string;
    error: {
        code: string;
        message: string;
        details?: unknown;
    };
    sessionId?: string | undefined;
}, {
    type: "error";
    timestamp: string;
    error: {
        code: string;
        message: string;
        details?: unknown;
    };
    sessionId?: string | undefined;
}>;
/**
 * Ping/Pong message schemas
 */
export declare const pingMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"ping">;
}, "strip", z.ZodTypeAny, {
    type: "ping";
    timestamp: string;
    sessionId?: string | undefined;
}, {
    type: "ping";
    timestamp: string;
    sessionId?: string | undefined;
}>;
export declare const pongMessageSchema: z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"pong">;
}, "strip", z.ZodTypeAny, {
    type: "pong";
    timestamp: string;
    sessionId?: string | undefined;
}, {
    type: "pong";
    timestamp: string;
    sessionId?: string | undefined;
}>;
/**
 * Union of all WebSocket message types
 */
export declare const websocketMessageSchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"connect">;
    sessionId: z.ZodOptional<z.ZodString>;
    clientType: z.ZodEnum<["cli", "agent"]>;
}, "strip", z.ZodTypeAny, {
    type: "connect";
    timestamp: string;
    clientType: "cli" | "agent";
    sessionId?: string | undefined;
}, {
    type: "connect";
    timestamp: string;
    clientType: "cli" | "agent";
    sessionId?: string | undefined;
}>, z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"disconnect">;
    sessionId: z.ZodString;
    reason: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "disconnect";
    timestamp: string;
    reason?: string | undefined;
}, {
    sessionId: string;
    type: "disconnect";
    timestamp: string;
    reason?: string | undefined;
}>, z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"agent_request">;
    sessionId: z.ZodString;
    payload: z.ZodObject<{
        sessionId: z.ZodString;
        type: z.ZodEnum<["confirmation", "information", "decision", "approval", "feedback"]>;
        priority: z.ZodEnum<["low", "normal", "high", "urgent"]>;
        summary: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        timeout: z.ZodNumber;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "agent_request";
    timestamp: string;
    payload: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    };
}, {
    sessionId: string;
    type: "agent_request";
    timestamp: string;
    payload: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    };
}>, z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"human_response">;
    sessionId: z.ZodString;
    payload: z.ZodObject<{
        sessionId: z.ZodString;
        response: z.ZodString;
        approved: z.ZodOptional<z.ZodBoolean>;
        data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        systemPrompt: z.ZodOptional<z.ZodString>;
        userPrompt: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }, {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "human_response";
    timestamp: string;
    payload: {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    };
}, {
    sessionId: string;
    type: "human_response";
    timestamp: string;
    payload: {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    };
}>, z.ZodObject<{
    timestamp: z.ZodString;
} & {
    type: z.ZodLiteral<"session_update">;
    sessionId: z.ZodString;
    status: z.ZodEnum<["active", "waiting", "completed", "timeout", "error"]>;
    message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "session_update";
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timestamp: string;
    message?: string | undefined;
}, {
    sessionId: string;
    type: "session_update";
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timestamp: string;
    message?: string | undefined;
}>, z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"error">;
    error: z.ZodObject<{
        code: z.ZodString;
        message: z.ZodString;
        details: z.ZodOptional<z.ZodUnknown>;
    }, "strip", z.ZodTypeAny, {
        code: string;
        message: string;
        details?: unknown;
    }, {
        code: string;
        message: string;
        details?: unknown;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "error";
    timestamp: string;
    error: {
        code: string;
        message: string;
        details?: unknown;
    };
    sessionId?: string | undefined;
}, {
    type: "error";
    timestamp: string;
    error: {
        code: string;
        message: string;
        details?: unknown;
    };
    sessionId?: string | undefined;
}>, z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"ping">;
}, "strip", z.ZodTypeAny, {
    type: "ping";
    timestamp: string;
    sessionId?: string | undefined;
}, {
    type: "ping";
    timestamp: string;
    sessionId?: string | undefined;
}>, z.ZodObject<{
    timestamp: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
} & {
    type: z.ZodLiteral<"pong">;
}, "strip", z.ZodTypeAny, {
    type: "pong";
    timestamp: string;
    sessionId?: string | undefined;
}, {
    type: "pong";
    timestamp: string;
    sessionId?: string | undefined;
}>]>;
/**
 * Type exports
 */
export type MessageType = z.infer<typeof messageTypeSchema>;
export type BaseMessage = z.infer<typeof baseMessageSchema>;
export type ConnectMessage = z.infer<typeof connectMessageSchema>;
export type DisconnectMessage = z.infer<typeof disconnectMessageSchema>;
export type AgentRequestMessage = z.infer<typeof agentRequestMessageSchema>;
export type HumanResponseMessage = z.infer<typeof humanResponseMessageSchema>;
export type SessionUpdateMessage = z.infer<typeof sessionUpdateMessageSchema>;
export type ErrorMessage = z.infer<typeof errorMessageSchema>;
export type PingMessage = z.infer<typeof pingMessageSchema>;
export type PongMessage = z.infer<typeof pongMessageSchema>;
export type WebSocketMessage = z.infer<typeof websocketMessageSchema>;
//# sourceMappingURL=websocket.d.ts.map