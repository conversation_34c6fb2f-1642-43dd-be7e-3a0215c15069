import { z } from 'zod';
import { sessionIdSchema, timestampSchema } from './base.js';
import { agentRequestSchema, humanResponseSchema } from './agent.js';
/**
 * WebSocket message types
 */
export const messageTypeSchema = z.enum([
    'connect',
    'disconnect',
    'agent_request',
    'human_response',
    'session_update',
    'error',
    'ping',
    'pong',
]);
/**
 * Base WebSocket message schema
 */
export const baseMessageSchema = z.object({
    type: messageTypeSchema,
    timestamp: timestampSchema,
    sessionId: sessionIdSchema.optional(),
});
/**
 * Connection message schema
 */
export const connectMessageSchema = baseMessageSchema.extend({
    type: z.literal('connect'),
    sessionId: sessionIdSchema.optional(), // Optional for CLI - server will assign
    clientType: z.enum(['cli', 'agent']),
});
/**
 * Disconnect message schema
 */
export const disconnectMessageSchema = baseMessageSchema.extend({
    type: z.literal('disconnect'),
    sessionId: sessionIdSchema,
    reason: z.string().optional(),
});
/**
 * Agent request message schema
 */
export const agentRequestMessageSchema = baseMessageSchema.extend({
    type: z.literal('agent_request'),
    sessionId: sessionIdSchema,
    payload: agentRequestSchema,
});
/**
 * Human response message schema
 */
export const humanResponseMessageSchema = baseMessageSchema.extend({
    type: z.literal('human_response'),
    sessionId: sessionIdSchema,
    payload: humanResponseSchema,
});
/**
 * Session update message schema
 */
export const sessionUpdateMessageSchema = baseMessageSchema.extend({
    type: z.literal('session_update'),
    sessionId: sessionIdSchema,
    status: z.enum(['active', 'waiting', 'completed', 'timeout', 'error']),
    message: z.string().optional(),
});
/**
 * Error message schema
 */
export const errorMessageSchema = baseMessageSchema.extend({
    type: z.literal('error'),
    error: z.object({
        code: z.string(),
        message: z.string(),
        details: z.unknown().optional(),
    }),
});
/**
 * Ping/Pong message schemas
 */
export const pingMessageSchema = baseMessageSchema.extend({
    type: z.literal('ping'),
});
export const pongMessageSchema = baseMessageSchema.extend({
    type: z.literal('pong'),
});
/**
 * Union of all WebSocket message types
 */
export const websocketMessageSchema = z.discriminatedUnion('type', [
    connectMessageSchema,
    disconnectMessageSchema,
    agentRequestMessageSchema,
    humanResponseMessageSchema,
    sessionUpdateMessageSchema,
    errorMessageSchema,
    pingMessageSchema,
    pongMessageSchema,
]);
//# sourceMappingURL=websocket.js.map