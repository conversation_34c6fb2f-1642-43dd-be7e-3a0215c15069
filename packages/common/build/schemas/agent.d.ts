import { z } from 'zod';
/**
 * Agent request priority levels
 */
export declare const prioritySchema: z.<PERSON><["low", "normal", "high", "urgent"]>;
/**
 * Agent request types
 */
export declare const requestTypeSchema: z.<PERSON><PERSON><["confirmation", "information", "decision", "approval", "feedback"]>;
/**
 * Schema for agent request input (with optional fields)
 */
export declare const agentRequestInputSchema: z.ZodObject<{
    sessionId: z.ZodString;
    type: z.<PERSON><PERSON><PERSON>num<["confirmation", "information", "decision", "approval", "feedback"]>;
    priority: z.<PERSON>od<PERSON>ptional<z.ZodEnum<["low", "normal", "high", "urgent"]>>;
    summary: z.ZodString;
    details: z.ZodOptional<z.ZodString>;
    context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
    timeout: z.<PERSON>odOptional<z.<PERSON>odN<PERSON>ber>;
    timestamp: z.ZodString;
}, "strip", z.<PERSON>odTypeAny, {
    sessionId: string;
    type: "confirmation" | "information" | "decision" | "approval" | "feedback";
    summary: string;
    timestamp: string;
    priority?: "low" | "normal" | "high" | "urgent" | undefined;
    details?: string | undefined;
    context?: Record<string, unknown> | undefined;
    timeout?: number | undefined;
}, {
    sessionId: string;
    type: "confirmation" | "information" | "decision" | "approval" | "feedback";
    summary: string;
    timestamp: string;
    priority?: "low" | "normal" | "high" | "urgent" | undefined;
    details?: string | undefined;
    context?: Record<string, unknown> | undefined;
    timeout?: number | undefined;
}>;
/**
 * Schema for agent requests to human (with all required fields)
 */
export declare const agentRequestSchema: z.ZodObject<{
    sessionId: z.ZodString;
    type: z.ZodEnum<["confirmation", "information", "decision", "approval", "feedback"]>;
    priority: z.ZodEnum<["low", "normal", "high", "urgent"]>;
    summary: z.ZodString;
    details: z.ZodOptional<z.ZodString>;
    context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
    timeout: z.ZodNumber;
    timestamp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    type: "confirmation" | "information" | "decision" | "approval" | "feedback";
    priority: "low" | "normal" | "high" | "urgent";
    summary: string;
    timeout: number;
    timestamp: string;
    details?: string | undefined;
    context?: Record<string, unknown> | undefined;
}, {
    sessionId: string;
    type: "confirmation" | "information" | "decision" | "approval" | "feedback";
    priority: "low" | "normal" | "high" | "urgent";
    summary: string;
    timeout: number;
    timestamp: string;
    details?: string | undefined;
    context?: Record<string, unknown> | undefined;
}>;
/**
 * Schema for human responses to agent
 */
export declare const humanResponseSchema: z.ZodObject<{
    sessionId: z.ZodString;
    response: z.ZodString;
    approved: z.ZodOptional<z.ZodBoolean>;
    data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
    systemPrompt: z.ZodOptional<z.ZodString>;
    userPrompt: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    timestamp: string;
    response: string;
    approved?: boolean | undefined;
    data?: Record<string, unknown> | undefined;
    systemPrompt?: string | undefined;
    userPrompt?: string | undefined;
}, {
    sessionId: string;
    timestamp: string;
    response: string;
    approved?: boolean | undefined;
    data?: Record<string, unknown> | undefined;
    systemPrompt?: string | undefined;
    userPrompt?: string | undefined;
}>;
/**
 * Schema for session status
 */
export declare const sessionStatusSchema: z.ZodEnum<["active", "waiting", "completed", "timeout", "error"]>;
/**
 * Schema for session information
 */
export declare const sessionSchema: z.ZodObject<{
    id: z.ZodString;
    status: z.ZodEnum<["active", "waiting", "completed", "timeout", "error"]>;
    agentId: z.ZodString;
    createdAt: z.ZodString;
    lastActivity: z.ZodString;
    timeout: z.ZodNumber;
    currentRequest: z.ZodOptional<z.ZodObject<{
        sessionId: z.ZodString;
        type: z.ZodEnum<["confirmation", "information", "decision", "approval", "feedback"]>;
        priority: z.ZodEnum<["low", "normal", "high", "urgent"]>;
        summary: z.ZodString;
        details: z.ZodOptional<z.ZodString>;
        context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
        timeout: z.ZodNumber;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }, {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timeout: number;
    id: string;
    agentId: string;
    createdAt: string;
    lastActivity: string;
    currentRequest?: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    } | undefined;
}, {
    status: "timeout" | "active" | "waiting" | "completed" | "error";
    timeout: number;
    id: string;
    agentId: string;
    createdAt: string;
    lastActivity: string;
    currentRequest?: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    } | undefined;
}>;
/**
 * Type exports
 */
export type Priority = z.infer<typeof prioritySchema>;
export type RequestType = z.infer<typeof requestTypeSchema>;
export type AgentRequestInput = z.infer<typeof agentRequestInputSchema>;
export type AgentRequest = z.infer<typeof agentRequestSchema>;
export type HumanResponse = z.infer<typeof humanResponseSchema>;
export type SessionStatus = z.infer<typeof sessionStatusSchema>;
export type Session = z.infer<typeof sessionSchema>;
//# sourceMappingURL=agent.d.ts.map