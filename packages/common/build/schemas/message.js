import { z } from 'zod';
/**
 * CLI message type enumeration
 */
export const CLIMessageTypeSchema = z.enum([
    'agent_request',
    'human_response',
    'system',
]);
/**
 * CLI message schema for display
 */
export const CLIMessageSchema = z.object({
    id: z.string().uuid(),
    type: CLIMessageTypeSchema,
    content: z.string(),
    timestamp: z.date(),
    sessionId: z.string().uuid().optional(),
});
//# sourceMappingURL=message.js.map