import { z } from 'zod';
import { sessionIdSchema, timestampSchema } from './base.js';

/**
 * Agent request priority levels
 */
export const prioritySchema = z.enum(['low', 'normal', 'high', 'urgent']);

/**
 * Agent request types
 */
export const requestTypeSchema = z.enum([
  'confirmation',
  'information',
  'decision',
  'approval',
  'feedback',
]);

/**
 * Schema for agent request input (with optional fields)
 */
export const agentRequestInputSchema = z.object({
  sessionId: sessionIdSchema,
  type: requestTypeSchema,
  priority: prioritySchema.optional(),
  summary: z
    .string()
    .min(10, 'Summary must be at least 10 characters')
    .max(1000, 'Summary cannot exceed 1000 characters'),
  details: z
    .string()
    .max(5000, 'Details cannot exceed 5000 characters')
    .optional(),
  context: z
    .record(z.string(), z.unknown())
    .optional()
    .describe('Additional context data'),
  timeout: z
    .number()
    .int()
    .min(30, 'Timeout must be at least 30 seconds')
    .max(3600, 'Timeout cannot exceed 1 hour')
    .optional(),
  timestamp: timestampSchema,
});

/**
 * Schema for agent requests to human (with all required fields)
 */
export const agentRequestSchema = z.object({
  sessionId: sessionIdSchema,
  type: requestTypeSchema,
  priority: prioritySchema,
  summary: z
    .string()
    .min(10, 'Summary must be at least 10 characters')
    .max(1000, 'Summary cannot exceed 1000 characters'),
  details: z
    .string()
    .max(5000, 'Details cannot exceed 5000 characters')
    .optional(),
  context: z
    .record(z.string(), z.unknown())
    .optional()
    .describe('Additional context data'),
  timeout: z
    .number()
    .int()
    .min(30, 'Timeout must be at least 30 seconds')
    .max(3600, 'Timeout cannot exceed 1 hour'),
  timestamp: timestampSchema,
});

/**
 * Schema for human responses to agent
 */
export const humanResponseSchema = z.object({
  sessionId: sessionIdSchema,
  response: z
    .string()
    .min(1, 'Response cannot be empty')
    .max(2000, 'Response cannot exceed 2000 characters'),
  approved: z.boolean().optional().describe('For approval-type requests'),
  data: z
    .record(z.string(), z.unknown())
    .optional()
    .describe('Additional response data'),
  systemPrompt: z
    .string()
    .optional()
    .describe('System prompt to remind agent of persistent context'),
  userPrompt: z
    .string()
    .optional()
    .describe('User prompt for additional context or instructions'),
  timestamp: timestampSchema,
});

/**
 * Schema for session status
 */
export const sessionStatusSchema = z.enum([
  'active',
  'waiting',
  'completed',
  'timeout',
  'error',
]);

/**
 * Schema for session information
 */
export const sessionSchema = z.object({
  id: sessionIdSchema,
  status: sessionStatusSchema,
  agentId: z.string().min(1, 'Agent ID cannot be empty'),
  createdAt: timestampSchema,
  lastActivity: timestampSchema,
  timeout: z.number().int().positive(),
  currentRequest: agentRequestSchema.optional(),
});

/**
 * Type exports
 */
export type Priority = z.infer<typeof prioritySchema>;
export type RequestType = z.infer<typeof requestTypeSchema>;
export type AgentRequestInput = z.infer<typeof agentRequestInputSchema>;
export type AgentRequest = z.infer<typeof agentRequestSchema>;
export type HumanResponse = z.infer<typeof humanResponseSchema>;
export type SessionStatus = z.infer<typeof sessionStatusSchema>;
export type Session = z.infer<typeof sessionSchema>;
