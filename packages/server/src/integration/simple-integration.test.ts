import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PowersteerServer } from '../server.js';
import { WebSocket } from 'ws';
import { AgentRequest, HumanResponse } from '@powersteer/common';

describe('PowersteerServer Integration', () => {
  let server: PowersteerServer;
  let wsClient: WebSocket;
  const wsPort = 8082;

  beforeEach(async () => {
    server = new PowersteerServer({
      session: {
        defaultTimeout: 30,
        maxSessions: 10,
        cleanupInterval: 10000,
      },
      messageQueue: {
        messageTimeout: 30,
        maxQueueSize: 100,
      },
      webSocket: {
        port: wsPort,
        pingInterval: 30000,
        connectionTimeout: 60000,
      },
    });

    await server.start();
    // Give server time to start
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  afterEach(async () => {
    if (wsClient && wsClient.readyState === WebSocket.OPEN) {
      wsClient.close();
    }
    if (server) {
      await server.stop();
    }
  });

  it('should start and stop server successfully', async () => {
    expect(server).toBeDefined();
    // Server should be running after beforeEach
    await server.stop();
    // Note: MCP server cannot be restarted once stopped, this is expected behavior
    // Just verify we can stop it cleanly
  });

  it('should accept WebSocket connections', async () => {
    return new Promise<void>((resolve, reject) => {
      wsClient = new WebSocket(`ws://localhost:${wsPort}`);
      
      wsClient.on('open', () => {
        expect(wsClient.readyState).toBe(WebSocket.OPEN);
        resolve();
      });
      
      wsClient.on('error', (error) => {
        reject(error);
      });
      
      // Timeout after 5 seconds
      setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 5000);
    });
  });

  it('should handle basic session creation via MCP server', async () => {
    // Create a session through the MCP server
    const sessionManager = (server as any).sessionManager;
    const session = await sessionManager.createSession();

    expect(session).toBeDefined();
    expect(session.id).toBeDefined();
    expect(session.status).toBe('active');

    // Verify session exists - getSession is async
    const retrievedSession = await sessionManager.getSession(session.id);
    expect(retrievedSession).toEqual(session);
  });

  it('should handle message queue operations', async () => {
    // Create a session
    const sessionManager = (server as any).sessionManager;
    const messageQueue = (server as any).messageQueue;
    const session = await sessionManager.createSession();
    
    // Create an agent request
    const agentRequest: AgentRequest = {
      id: 'test-request-1',
      sessionId: session.id,
      type: 'human_input',
      message: 'Test message',
      timestamp: new Date().toISOString(),
    };
    
    // Enqueue the request
    const promise = messageQueue.enqueueAgentRequest(session.id, agentRequest);
    
    // Provide a response
    const humanResponse: HumanResponse = {
      id: 'test-response-1',
      requestId: agentRequest.id,
      sessionId: session.id,
      type: 'text',
      content: 'Test response',
      timestamp: new Date().toISOString(),
    };
    
    // Simulate async response
    setTimeout(() => {
      messageQueue.enqueueHumanResponse(session.id, humanResponse);
    }, 100);
    
    // Wait for response
    const response = await promise;
    expect(response).toEqual(humanResponse);
  });
});
