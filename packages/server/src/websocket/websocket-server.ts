import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import {
  WebSocketMessage,
  websocketMessageSchema,
  SessionId,
  AgentRequest,
  HumanResponse,
  safeValidate,
  createTimestamp,
  MCPError,
  createWebSocketError,
  createSessionNotFoundError,
  withErrorHandling,
} from '@powersteer/common';
import { SessionManager } from '../session/session-manager.js';
import { MessageQueue } from '../session/message-queue.js';

/**
 * WebSocket connection info
 */
interface ConnectionInfo {
  ws: WebSocket;
  sessionId?: SessionId;
  clientType?: 'cli' | 'agent';
  lastPing: number;
}

/**
 * WebSocket server configuration
 */
export interface WebSocketServerConfig {
  port: number;
  pingInterval: number;
  connectionTimeout: number;
}

/**
 * WebSocket server for CLI communication
 */
export class PowersteerWebSocketServer {
  private wss: WebSocketServer;
  private connections = new Map<WebSocket, ConnectionInfo>();
  private sessionConnections = new Map<SessionId, WebSocket>();
  private pingTimer?: NodeJS.Timeout;

  constructor(
    private config: WebSocketServerConfig,
    private sessionManager: SessionManager,
    private messageQueue: MessageQueue
  ) {
    this.wss = new WebSocketServer({ port: config.port });
    this.setupWebSocketHandlers();
    this.startPingTimer();
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    this.wss.on('connection', (ws: WebSocket, request: IncomingMessage) => {
      console.error(`New WebSocket connection from ${request.socket.remoteAddress}`);
      
      const connectionInfo: ConnectionInfo = {
        ws,
        lastPing: Date.now(),
      };
      
      this.connections.set(ws, connectionInfo);

      ws.on('message', (data: Buffer) => {
        this.handleMessage(ws, data).catch(error => {
          console.error('Error handling WebSocket message:', error);
          this.sendError(ws, error);
        });
      });

      ws.on('pong', () => {
        const info = this.connections.get(ws);
        if (info) {
          info.lastPing = Date.now();
        }
      });

      ws.on('close', () => {
        this.handleDisconnection(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.handleDisconnection(ws);
      });
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage = withErrorHandling(async (ws: WebSocket, data: Buffer): Promise<void> => {
    let message: WebSocketMessage;
    
    try {
      const rawMessage = JSON.parse(data.toString());
      const validation = safeValidate(websocketMessageSchema, rawMessage);

      if (!validation.success) {
        throw createWebSocketError(
          'Invalid message format',
          validation.error.details
        );
      }

      message = validation.data;
    } catch (error) {
      throw createWebSocketError(
        'Failed to parse WebSocket message',
        error
      );
    }

    switch (message.type) {
      case 'connect':
        await this.handleConnect(ws, message);
        break;
      
      case 'disconnect':
        await this.handleDisconnect(ws, message);
        break;
      
      case 'human_response':
        await this.handleHumanResponse(ws, message);
        break;
      
      case 'ping':
        await this.handlePing(ws, message);
        break;
      
      default:
        throw createWebSocketError(`Unknown message type: ${(message as any).type}`);
    }
  });

  /**
   * Handle client connection
   */
  private handleConnect = withErrorHandling(async (ws: WebSocket, message: any): Promise<void> => {
    console.error('DEBUG: handleConnect called with message:', JSON.stringify(message, null, 2));
    const { sessionId: providedSessionId, clientType } = message;
    let sessionId = providedSessionId;

    // For CLI connections with sessionId, create the session
    if (sessionId && clientType === 'cli') {
      try {
        // Try to get existing session
        await this.sessionManager.getSession(sessionId);
        console.error('DEBUG: Using existing session:', sessionId);
      } catch (error) {
        // Session doesn't exist, create it with the provided ID
        console.error('DEBUG: Creating new session for CLI with ID:', sessionId);
        await this.sessionManager.createSessionWithId(sessionId, 'cli-generated');
        console.error('DEBUG: Successfully created session:', sessionId);
      }
    }
    // For CLI connections without sessionId, use the single session (legacy behavior)
    else if (!sessionId && clientType === 'cli') {
      const sessions = await this.sessionManager.listSessions();

      if (sessions.length === 0) {
        throw createWebSocketError('No sessions available. Please ensure an agent has created a session first.');
      }

      // Use the first (and should be only) session
      sessionId = sessions[0].id;
    }
    // For agent connections, verify session exists
    else if (sessionId) {
      await this.sessionManager.getSession(sessionId);
    }

    const connectionInfo = this.connections.get(ws);
    if (connectionInfo) {
      connectionInfo.sessionId = sessionId;
      connectionInfo.clientType = clientType;
    }

    // Map session to connection
    this.sessionConnections.set(sessionId, ws);

    // Send confirmation
    this.sendMessage(ws, {
      type: 'session_update',
      sessionId,
      status: 'active',
      message: 'Connected successfully',
      timestamp: createTimestamp(),
    });

    // Send any pending requests
    const pendingRequests = await this.messageQueue.getPendingRequests(sessionId);
    for (const request of pendingRequests) {
      // Ensure the request has all required fields
      const completeRequest: AgentRequest = {
        ...request,
        priority: request.priority ?? 'normal',
        timeout: request.timeout ?? 300,
      };

      this.sendMessage(ws, {
        type: 'agent_request',
        sessionId,
        payload: completeRequest,
        timestamp: createTimestamp(),
      });
    }
  });

  /**
   * Handle client disconnection message
   */
  private handleDisconnect = withErrorHandling(async (ws: WebSocket, message: any): Promise<void> => {
    const { sessionId, reason } = message;
    
    this.sessionConnections.delete(sessionId);
    
    const connectionInfo = this.connections.get(ws);
    if (connectionInfo) {
      connectionInfo.sessionId = undefined;
      connectionInfo.clientType = undefined;
    }
    
    console.error(`Client disconnected from session ${sessionId}: ${reason || 'No reason provided'}`);
  });

  /**
   * Handle human response
   */
  private handleHumanResponse = withErrorHandling(async (ws: WebSocket, message: any): Promise<void> => {
    const { sessionId, payload } = message;
    
    // Verify session exists and connection is authorized
    await this.sessionManager.getSession(sessionId);
    
    const connectionInfo = this.connections.get(ws);
    if (!connectionInfo || connectionInfo.sessionId !== sessionId) {
      throw createWebSocketError('Unauthorized: Session mismatch');
    }
    
    // Process human response
    await this.messageQueue.enqueueHumanResponse(sessionId, payload);
    
    // Send confirmation
    this.sendMessage(ws, {
      type: 'session_update',
      sessionId,
      status: 'active',
      message: 'Response received',
      timestamp: createTimestamp(),
    });
  });

  /**
   * Handle ping message
   */
  private handlePing = withErrorHandling(async (ws: WebSocket, message: any): Promise<void> => {
    this.sendMessage(ws, {
      type: 'pong',
      timestamp: createTimestamp(),
    });
  });

  /**
   * Handle WebSocket disconnection
   */
  private handleDisconnection(ws: WebSocket): void {
    const connectionInfo = this.connections.get(ws);
    
    if (connectionInfo?.sessionId) {
      this.sessionConnections.delete(connectionInfo.sessionId);
      console.error(`WebSocket disconnected from session ${connectionInfo.sessionId}`);
    }
    
    this.connections.delete(ws);
  }

  /**
   * Send message to WebSocket client
   */
  private sendMessage(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Send error message to WebSocket client
   */
  private sendError(ws: WebSocket, error: unknown): void {
    const mcpError = error instanceof MCPError ? error : new MCPError(
      'INTERNAL_ERROR',
      error instanceof Error ? error.message : 'Unknown error'
    );

    this.sendMessage(ws, {
      type: 'error',
      error: mcpError.toErrorObject(),
      timestamp: createTimestamp(),
    });
  }

  /**
   * Broadcast agent request to connected CLI
   */
  async broadcastAgentRequest(sessionId: SessionId, request: AgentRequest): Promise<void> {
    console.error(`DEBUG: broadcastAgentRequest called for session ${sessionId}`);
    const ws = this.sessionConnections.get(sessionId);
    console.error(`DEBUG: Found WebSocket connection: ${ws ? 'YES' : 'NO'}, readyState: ${ws?.readyState}`);

    if (ws && ws.readyState === WebSocket.OPEN) {
      // Ensure the request has all required fields
      const completeRequest: AgentRequest = {
        ...request,
        priority: request.priority ?? 'normal',
        timeout: request.timeout ?? 300,
      };

      console.error(`DEBUG: Sending agent request to CLI: ${JSON.stringify(completeRequest.summary)}`);
      this.sendMessage(ws, {
        type: 'agent_request',
        sessionId,
        payload: completeRequest,
        timestamp: createTimestamp(),
      });
    } else {
      console.error(`DEBUG: Cannot broadcast - no open WebSocket connection for session ${sessionId}`);
    }
  }

  /**
   * Start ping timer to keep connections alive
   */
  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      const now = Date.now();
      const timeout = this.config.connectionTimeout;
      
      for (const [ws, info] of this.connections.entries()) {
        if (now - info.lastPing > timeout) {
          console.error('Connection timeout, closing WebSocket');
          ws.terminate();
        } else if (ws.readyState === WebSocket.OPEN) {
          ws.ping();
        }
      }
    }, this.config.pingInterval);
  }

  /**
   * Get server statistics
   */
  getStats(): {
    totalConnections: number;
    activeConnections: number;
    sessionConnections: number;
  } {
    let activeConnections = 0;
    
    for (const [ws] of this.connections.entries()) {
      if (ws.readyState === WebSocket.OPEN) {
        activeConnections++;
      }
    }
    
    return {
      totalConnections: this.connections.size,
      activeConnections,
      sessionConnections: this.sessionConnections.size,
    };
  }

  /**
   * Stop the WebSocket server
   */
  async stop(): Promise<void> {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }
    
    // Close all connections
    for (const [ws] of this.connections.entries()) {
      ws.close();
    }
    
    // Close server
    return new Promise((resolve) => {
      this.wss.close(() => {
        console.error('WebSocket server stopped');
        resolve();
      });
    });
  }
}
