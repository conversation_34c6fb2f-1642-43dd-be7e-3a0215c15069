import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SessionManager, SessionConfig } from './session-manager.js';

describe('SessionManager', () => {
  let sessionManager: SessionManager;
  const config: SessionConfig = {
    defaultTimeout: 60, // 1 minute for testing
    maxSessions: 5,
    cleanupInterval: 1000, // 1 second for testing
  };

  beforeEach(() => {
    sessionManager = new SessionManager(config);
  });

  afterEach(() => {
    sessionManager.destroy();
  });

  describe('createSession', () => {
    it('should create a new session', async () => {
      const agentId = 'test-agent-1';
      const session = await sessionManager.createSession(agentId);

      expect(session.id).toBeDefined();
      expect(session.agentId).toBe(agentId);
      expect(session.status).toBe('active');
      expect(session.timeout).toBe(config.defaultTimeout);
      expect(session.createdAt).toBeDefined();
      expect(session.lastActivity).toBeDefined();
    });

    it('should reject when max sessions reached', async () => {
      // Create max sessions
      for (let i = 0; i < config.maxSessions; i++) {
        await sessionManager.createSession(`agent-${i}`);
      }

      // Try to create one more
      await expect(
        sessionManager.createSession('overflow-agent')
      ).rejects.toThrow('Maximum number of sessions reached');
    });
  });

  describe('getSession', () => {
    it('should retrieve existing session', async () => {
      const agentId = 'test-agent-1';
      const created = await sessionManager.createSession(agentId);
      const retrieved = await sessionManager.getSession(created.id);

      expect(retrieved).toEqual(created);
    });

    it('should throw error for non-existent session', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-426614174000';
      
      await expect(
        sessionManager.getSession(fakeId as any)
      ).rejects.toThrow('Session not found');
    });
  });

  describe('updateSessionStatus', () => {
    it('should update session status', async () => {
      const session = await sessionManager.createSession('test-agent');

      // Add small delay to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      const updated = await sessionManager.updateSessionStatus(
        session.id,
        'waiting'
      );

      expect(updated.status).toBe('waiting');
      expect(updated.lastActivity).not.toBe(session.lastActivity);
    });

    it('should handle current request', async () => {
      const session = await sessionManager.createSession('test-agent');
      const request = {
        sessionId: session.id,
        type: 'confirmation' as const,
        priority: 'normal' as const,
        summary: 'Test request',
        timeout: 300,
        timestamp: new Date().toISOString(),
      };

      const updated = await sessionManager.updateSessionStatus(
        session.id,
        'waiting',
        request
      );

      expect(updated.status).toBe('waiting');
      expect(updated.currentRequest).toEqual(request);
    });
  });

  describe('setCurrentRequest', () => {
    it('should set current request and update status to waiting', async () => {
      const session = await sessionManager.createSession('test-agent');
      const request = {
        sessionId: session.id,
        type: 'information' as const,
        priority: 'high' as const,
        summary: 'Need information about X',
        timeout: 300,
        timestamp: new Date().toISOString(),
      };

      const updated = await sessionManager.setCurrentRequest(session.id, request);

      expect(updated.status).toBe('waiting');
      expect(updated.currentRequest).toEqual(request);
    });
  });

  describe('clearCurrentRequest', () => {
    it('should clear current request and set status to active', async () => {
      const session = await sessionManager.createSession('test-agent');
      const request = {
        sessionId: session.id,
        type: 'confirmation' as const,
        priority: 'normal' as const,
        summary: 'Test request',
        timeout: 300,
        timestamp: new Date().toISOString(),
      };

      await sessionManager.setCurrentRequest(session.id, request);
      const cleared = await sessionManager.clearCurrentRequest(session.id);

      expect(cleared.status).toBe('active');
      expect(cleared.currentRequest).toBeUndefined();
    });
  });

  describe('removeSession', () => {
    it('should remove session', async () => {
      const session = await sessionManager.createSession('test-agent');
      
      expect(sessionManager.hasSession(session.id)).toBe(true);
      
      await sessionManager.removeSession(session.id);
      
      expect(sessionManager.hasSession(session.id)).toBe(false);
    });
  });

  describe('getActiveSessions', () => {
    it('should return only active and waiting sessions', async () => {
      const session1 = await sessionManager.createSession('agent-1');
      const session2 = await sessionManager.createSession('agent-2');
      const session3 = await sessionManager.createSession('agent-3');

      // Update statuses
      await sessionManager.updateSessionStatus(session1.id, 'active');
      await sessionManager.updateSessionStatus(session2.id, 'waiting');
      await sessionManager.updateSessionStatus(session3.id, 'completed');

      const activeSessions = await sessionManager.getActiveSessions();

      expect(activeSessions).toHaveLength(2);
      expect(activeSessions.map(s => s.id)).toContain(session1.id);
      expect(activeSessions.map(s => s.id)).toContain(session2.id);
      expect(activeSessions.map(s => s.id)).not.toContain(session3.id);
    });
  });

  describe('getSessionCount', () => {
    it('should return correct session count', async () => {
      expect(sessionManager.getSessionCount()).toBe(0);

      await sessionManager.createSession('agent-1');
      expect(sessionManager.getSessionCount()).toBe(1);

      await sessionManager.createSession('agent-2');
      expect(sessionManager.getSessionCount()).toBe(2);

      const session = await sessionManager.createSession('agent-3');
      await sessionManager.removeSession(session.id);
      expect(sessionManager.getSessionCount()).toBe(2);
    });
  });

  describe('hasSession', () => {
    it('should correctly check session existence', async () => {
      const session = await sessionManager.createSession('test-agent');
      
      expect(sessionManager.hasSession(session.id)).toBe(true);
      
      await sessionManager.removeSession(session.id);
      
      expect(sessionManager.hasSession(session.id)).toBe(false);
    });
  });

  describe('session timeout handling', () => {
    it('should handle session timeout', async () => {
      // Use a very short timeout for testing
      const shortTimeoutManager = new SessionManager({
        ...config,
        defaultTimeout: 1, // 1 second
      });

      try {
        const session = await shortTimeoutManager.createSession('test-agent');
        
        // Wait for timeout
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        const retrieved = await shortTimeoutManager.getSession(session.id);
        expect(retrieved.status).toBe('timeout');
        
      } finally {
        shortTimeoutManager.destroy();
      }
    });
  });
});
