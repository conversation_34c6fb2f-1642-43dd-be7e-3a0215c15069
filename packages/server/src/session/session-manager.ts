import { v4 as uuidv4 } from 'uuid';
import {
  Session,
  SessionId,
  SessionStatus,
  AgentRequest,
  HumanResponse,
  createTimestamp,
  MCPError,
  createSessionNotFoundError,
  createSessionTimeoutError,
  withErrorHandling,
} from '@powersteer/common';

/**
 * Session configuration options
 */
export interface SessionConfig {
  defaultTimeout: number;
  maxSessions: number;
  cleanupInterval: number;
}

/**
 * Session manager for handling multiple concurrent agent sessions
 */
export class SessionManager {
  private sessions = new Map<SessionId, Session>();
  private timeouts = new Map<SessionId, NodeJS.Timeout>();
  private cleanupTimer?: NodeJS.Timeout;

  constructor(private config: SessionConfig) {
    this.startCleanupTimer();
  }

  /**
   * Create a new session for an agent
   */
  createSession = withErrorHandling(async (agentId: string): Promise<Session> => {
    if (this.sessions.size >= this.config.maxSessions) {
      throw new MCPError(
        'INTERNAL_ERROR',
        'Maximum number of sessions reached',
        { maxSessions: this.config.maxSessions }
      );
    }

    const sessionId = uuidv4() as SessionId;
    const now = createTimestamp();

    const session: Session = {
      id: sessionId,
      status: 'active',
      agentId,
      createdAt: now,
      lastActivity: now,
      timeout: this.config.defaultTimeout,
    };

    this.sessions.set(sessionId, session);
    this.setSessionTimeout(sessionId);

    return session;
  });

  /**
   * Create a new session with a specific ID (for CLI-generated sessions)
   */
  createSessionWithId = withErrorHandling(async (sessionId: SessionId, agentId: string): Promise<Session> => {
    if (this.sessions.size >= this.config.maxSessions) {
      throw new MCPError(
        'INTERNAL_ERROR',
        'Maximum number of sessions reached',
        { maxSessions: this.config.maxSessions }
      );
    }

    if (this.sessions.has(sessionId)) {
      throw new MCPError(
        'INTERNAL_ERROR',
        'Session with this ID already exists',
        { sessionId }
      );
    }

    const now = createTimestamp();

    const session: Session = {
      id: sessionId,
      status: 'active',
      agentId,
      createdAt: now,
      lastActivity: now,
      timeout: this.config.defaultTimeout,
    };

    this.sessions.set(sessionId, session);
    this.setSessionTimeout(sessionId);

    return session;
  });

  /**
   * Get session by ID
   */
  getSession = withErrorHandling(async (sessionId: SessionId): Promise<Session> => {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw createSessionNotFoundError(sessionId);
    }
    return { ...session };
  });

  /**
   * List all sessions
   */
  listSessions = withErrorHandling(async (): Promise<Session[]> => {
    return Array.from(this.sessions.values()).map(session => ({ ...session }));
  });

  /**
   * Update session status
   */
  updateSessionStatus = withErrorHandling(async (
    sessionId: SessionId,
    status: SessionStatus,
    currentRequest?: AgentRequest
  ): Promise<Session> => {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw createSessionNotFoundError(sessionId);
    }

    const updatedSession: Session = {
      ...session,
      status,
      lastActivity: createTimestamp(),
      currentRequest,
    };

    this.sessions.set(sessionId, updatedSession);
    
    // Reset timeout for active sessions
    if (status === 'active' || status === 'waiting') {
      this.setSessionTimeout(sessionId);
    } else {
      this.clearSessionTimeout(sessionId);
    }

    return { ...updatedSession };
  });

  /**
   * Set current request for a session
   */
  setCurrentRequest = withErrorHandling(async (
    sessionId: SessionId,
    request: AgentRequest
  ): Promise<Session> => {
    return this.updateSessionStatus(sessionId, 'waiting', request);
  });

  /**
   * Clear current request for a session
   */
  clearCurrentRequest = withErrorHandling(async (sessionId: SessionId): Promise<Session> => {
    return this.updateSessionStatus(sessionId, 'active');
  });

  /**
   * Remove session
   */
  removeSession = withErrorHandling(async (sessionId: SessionId): Promise<void> => {
    this.clearSessionTimeout(sessionId);
    this.sessions.delete(sessionId);
  });

  /**
   * Get all active sessions
   */
  getActiveSessions = withErrorHandling(async (): Promise<Session[]> => {
    return Array.from(this.sessions.values())
      .filter(session => session.status === 'active' || session.status === 'waiting')
      .map(session => ({ ...session }));
  });

  /**
   * Get session count
   */
  getSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Check if session exists
   */
  hasSession(sessionId: SessionId): boolean {
    return this.sessions.has(sessionId);
  }

  /**
   * Cleanup expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: SessionId[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      const lastActivity = new Date(session.lastActivity).getTime();
      const timeoutMs = session.timeout * 1000;
      
      if (now - lastActivity > timeoutMs) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.handleSessionTimeout(sessionId);
    }
  }

  /**
   * Handle session timeout
   */
  private handleSessionTimeout(sessionId: SessionId): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Update status to timeout
      this.sessions.set(sessionId, {
        ...session,
        status: 'timeout',
        lastActivity: createTimestamp(),
      });
      
      // Clear timeout and remove after a grace period
      this.clearSessionTimeout(sessionId);
      setTimeout(() => {
        this.sessions.delete(sessionId);
      }, 30000); // 30 second grace period
    }
  }

  /**
   * Set session timeout
   */
  private setSessionTimeout(sessionId: SessionId): void {
    this.clearSessionTimeout(sessionId);
    
    const session = this.sessions.get(sessionId);
    if (session) {
      const timeout = setTimeout(() => {
        this.handleSessionTimeout(sessionId);
      }, session.timeout * 1000);
      
      this.timeouts.set(sessionId, timeout);
    }
  }

  /**
   * Clear session timeout
   */
  private clearSessionTimeout(sessionId: SessionId): void {
    const timeout = this.timeouts.get(sessionId);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(sessionId);
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup timer and clear all sessions
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // Clear all timeouts
    for (const timeout of this.timeouts.values()) {
      clearTimeout(timeout);
    }
    
    this.timeouts.clear();
    this.sessions.clear();
  }
}
