{"name": "@powersteer/server", "version": "1.0.0", "description": "Powersteer MCP server for agent communication", "type": "module", "main": "./build/index.js", "types": "./build/index.d.ts", "bin": {"powersteer-server": "./build/cli.js", "powersteer-mcp": "./build/mcp/mcp-cli.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node build/cli.js", "start:mcp": "node build/mcp/mcp-cli.js", "test": "vitest run", "test:watch": "vitest", "clean": "rm -rf build"}, "dependencies": {"@powersteer/common": "file:../common", "@modelcontextprotocol/sdk": "^0.5.0", "ws": "^8.14.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/ws": "^8.5.10", "@types/uuid": "^9.0.7"}, "files": ["build"]}