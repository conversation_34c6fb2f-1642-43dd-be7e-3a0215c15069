{"version": 3, "file": "websocket-server.js", "sourceRoot": "", "sources": ["../../src/websocket/websocket-server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AAEhD,OAAO,EAEL,sBAAsB,EAItB,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,oBAAoB,EAEpB,iBAAiB,GAClB,MAAM,oBAAoB,CAAC;AAuB5B;;GAEG;AACH,MAAM,OAAO,yBAAyB;IAO1B;IACA;IACA;IARF,GAAG,CAAkB;IACrB,WAAW,GAAG,IAAI,GAAG,EAA6B,CAAC;IACnD,kBAAkB,GAAG,IAAI,GAAG,EAAwB,CAAC;IACrD,SAAS,CAAkB;IAEnC,YACU,MAA6B,EAC7B,cAA8B,EAC9B,YAA0B;QAF1B,WAAM,GAAN,MAAM,CAAuB;QAC7B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,iBAAY,GAAZ,YAAY,CAAc;QAElC,IAAI,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAa,EAAE,OAAwB,EAAE,EAAE;YACpE,OAAO,CAAC,KAAK,CAAC,iCAAiC,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAE/E,MAAM,cAAc,GAAmB;gBACrC,EAAE;gBACF,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;aACrB,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAEzC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAY,EAAE,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACzC,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBACjB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtC,IAAI,IAAI,EAAE,CAAC;oBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAClB,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAa,EAAE,IAAY,EAAiB,EAAE;QAC7F,IAAI,OAAyB,CAAC;QAE9B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,YAAY,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;YAEpE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,oBAAoB,CACxB,wBAAwB,EACxB,UAAU,CAAC,KAAK,CAAC,OAAO,CACzB,CAAC;YACJ,CAAC;YAED,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,oBAAoB,CACxB,mCAAmC,EACnC,KAAK,CACN,CAAC;QACJ,CAAC;QAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACtC,MAAM;YAER,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACzC,MAAM;YAER,KAAK,gBAAgB;gBACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC5C,MAAM;YAER,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACnC,MAAM;YAER;gBACE,MAAM,oBAAoB,CAAC,yBAA0B,OAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAa,EAAE,OAAY,EAAiB,EAAE;QAC7F,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAC7D,IAAI,SAAS,GAAG,iBAAiB,CAAC;QAElC,yDAAyD;QACzD,IAAI,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC;gBACH,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAChD,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC;YAC7D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,wDAAwD;gBACxD,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,SAAS,CAAC,CAAC;gBACzE,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;gBAC1E,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,SAAS,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QACD,kFAAkF;aAC7E,IAAI,CAAC,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAE1D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,oBAAoB,CAAC,4EAA4E,CAAC,CAAC;YAC3G,CAAC;YAED,6CAA6C;YAC7C,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,CAAC;QACD,+CAA+C;aAC1C,IAAI,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChD,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC;YACrC,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;QACzC,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE3C,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,IAAI,EAAE,gBAAgB;YACtB,SAAS;YACT,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,wBAAwB;YACjC,SAAS,EAAE,eAAe,EAAE;SAC7B,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9E,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,6CAA6C;YAC7C,MAAM,eAAe,GAAiB;gBACpC,GAAG,OAAO;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ;gBACtC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,GAAG;aAChC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;gBACnB,IAAI,EAAE,eAAe;gBACrB,SAAS;gBACT,OAAO,EAAE,eAAe;gBACxB,SAAS,EAAE,eAAe,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAa,EAAE,OAAY,EAAiB,EAAE;QAChG,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEtC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE1C,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChD,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC;YACrC,cAAc,CAAC,UAAU,GAAG,SAAS,CAAC;QACxC,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,oCAAoC,SAAS,KAAK,MAAM,IAAI,oBAAoB,EAAE,CAAC,CAAC;IACpG,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,mBAAmB,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAa,EAAE,OAAY,EAAiB,EAAE;QACnG,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAEvC,qDAAqD;QACrD,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC9D,MAAM,oBAAoB,CAAC,gCAAgC,CAAC,CAAC;QAC/D,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEjE,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,IAAI,EAAE,gBAAgB;YACtB,SAAS;YACT,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,eAAe,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,UAAU,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAa,EAAE,OAAY,EAAiB,EAAE;QAC1F,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,eAAe,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,mBAAmB,CAAC,EAAa;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhD,IAAI,cAAc,EAAE,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzD,OAAO,CAAC,KAAK,CAAC,uCAAuC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,EAAa,EAAE,OAAyB;QAC1D,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACrC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,EAAa,EAAE,KAAc;QAC7C,MAAM,QAAQ,GAAG,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,CAC/D,gBAAgB,EAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CACzD,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,QAAQ,CAAC,aAAa,EAAE;YAC/B,SAAS,EAAE,eAAe,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,SAAoB,EAAE,OAAqB;QACrE,OAAO,CAAC,KAAK,CAAC,mDAAmD,SAAS,EAAE,CAAC,CAAC;QAC9E,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClD,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,iBAAiB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QAExG,IAAI,EAAE,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YAC3C,6CAA6C;YAC7C,MAAM,eAAe,GAAiB;gBACpC,GAAG,OAAO;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ;gBACtC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,GAAG;aAChC,CAAC;YAEF,OAAO,CAAC,KAAK,CAAC,wCAAwC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACjG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;gBACnB,IAAI,EAAE,eAAe;gBACrB,SAAS;gBACT,OAAO,EAAE,eAAe;gBACxB,SAAS,EAAE,eAAe,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,sEAAsE,SAAS,EAAE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAE9C,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpD,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC;oBAClC,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;oBACvD,EAAE,CAAC,SAAS,EAAE,CAAC;gBACjB,CAAC;qBAAM,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;oBAC5C,EAAE,CAAC,IAAI,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,QAAQ;QAKN,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACrC,iBAAiB,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YACvC,iBAAiB;YACjB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;SACjD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChC,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9C,EAAE,CAAC,KAAK,EAAE,CAAC;QACb,CAAC;QAED,eAAe;QACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE;gBAClB,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC1C,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}