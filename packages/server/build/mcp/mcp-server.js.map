{"version": 3, "file": "mcp-server.js", "sourceRoot": "", "sources": ["../../src/mcp/mcp-server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,MAAM,oCAAoC,CAAC;AACnG,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EACL,uBAAuB,EACvB,kBAAkB,EAMlB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,sBAAsB,EACtB,qBAAqB,EACrB,iBAAiB,GAClB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAE7C;;GAEG;AACH,MAAM,uBAAuB,GAAG,CAAC,CAAC,MAAM,CAAC;IACvC,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACvC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;IAClD,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACjF,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IACvE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;IACrC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACxC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE;IACrD,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;CACzD,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAMpB;IACA;IANF,MAAM,CAAS;IACf,SAAS,CAAuB;IAChC,eAAe,CAA6B;IAEpD,YACU,cAA8B,EAC9B,YAA0B;QAD1B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,iBAAY,GAAZ,YAAY,CAAc;QAElC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CACtB;YACE,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE,OAAO;SACjB,EACD;YACE,YAAY,EAAE;gBACZ,KAAK,EAAE,EAAE;aACV;SACF,CACF,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC5C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,eAA0C;QAC3D,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YAC/D,OAAO;gBACL,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,qBAAqB;wBAC3B,WAAW,EAAE,6aAA6a;wBAC1b,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,SAAS,EAAE;oCACT,IAAI,EAAE,QAAQ;oCACd,MAAM,EAAE,MAAM;oCACd,WAAW,EAAE,uDAAuD;iCACrE;gCACD,OAAO,EAAE;oCACP,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,oDAAoD;iCAClE;gCACD,IAAI,EAAE;oCACJ,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;oCACzE,WAAW,EAAE,8CAA8C;iCAC5D;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;oCACzC,OAAO,EAAE,QAAQ;oCACjB,WAAW,EAAE,+BAA+B;iCAC7C;gCACD,OAAO,EAAE;oCACP,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,EAAE;oCACb,SAAS,EAAE,IAAI;oCACf,WAAW,EAAE,mOAAmO;iCACjP;gCACD,OAAO,EAAE;oCACP,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,IAAI;oCACf,WAAW,EAAE,0CAA0C;iCACxD;gCACD,OAAO,EAAE;oCACP,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,kCAAkC;iCAChD;gCACD,OAAO,EAAE;oCACP,IAAI,EAAE,QAAQ;oCACd,OAAO,EAAE,GAAG;oCACZ,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,IAAI;oCACb,WAAW,EAAE,+CAA+C;iCAC7D;6BACF;4BACD,QAAQ,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC;yBACzC;qBACF;iBACF;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACrE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAEjD,IAAI,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;YAED,MAAM,sBAAsB,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAa,EAAE,EAAE;QAC1E,4BAA4B;QAC5B,MAAM,UAAU,GAAG,YAAY,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,qBAAqB,CACzB,4CAA4C,EAC5C,UAAU,CAAC,KAAK,CAAC,OAAO,CACzB,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/B,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;QAEpC,wBAAwB;QACxB,IAAI,SAAoB,CAAC;QACzB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,SAAS,GAAG,MAAM,CAAC,SAAsB,CAAC;YAC1C,wBAAwB;YACxB,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAE1D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,2BAA2B;gBAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACxE,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,MAAM,iBAAiB,GAAsB;YAC3C,SAAS;YACT,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,SAAS;SACV,CAAC;QAEF,qBAAqB;QACrB,MAAM,eAAe,GAAG,YAAY,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;QACjF,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,qBAAqB,CACzB,6BAA6B,EAC7B,eAAe,CAAC,KAAK,CAAC,OAAO,CAC9B,CAAC;QACJ,CAAC;QAED,8CAA8C;QAC9C,MAAM,YAAY,GAAiB;YACjC,GAAG,eAAe,CAAC,IAAI;YACvB,QAAQ,EAAE,eAAe,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ;YACnD,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,IAAI,GAAG;SAC7C,CAAC;QAEF,gCAAgC;QAChC,MAAM,iBAAiB,GAAG,YAAY,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QACzE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC/B,MAAM,qBAAqB,CACzB,iCAAiC,EACjC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAChC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAErE,8CAA8C;YAC9C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,OAAO,CAAC,KAAK,CAAC,gDAAgD,SAAS,EAAE,CAAC,CAAC;gBAC3E,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YACzE,CAAC;YAED,8CAA8C;YAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAC/D,SAAS,EACT,YAAY,CACb,CAAC;YAEF,qCAAqC;YACrC,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAEzD,0EAA0E;YAC1E,IAAI,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC;YACxC,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC7B,UAAU,GAAG,GAAG,aAAa,CAAC,QAAQ,OAAO,aAAa,CAAC,UAAU,EAAE,CAAC;YAC1E,CAAC;YAED,2BAA2B;YAC3B,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO,EAAE,IAAI;4BACb,SAAS;4BACT,UAAU,EAAE,8CAA8C;4BAC1D,QAAQ,EAAE,aAAa,CAAC,QAAQ;4BAChC,IAAI,EAAE,aAAa,CAAC,IAAI;4BACxB,YAAY,EAAE,aAAa,CAAC,YAAY;4BACxC,SAAS,EAAE,aAAa,CAAC,SAAS;yBACnC,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iCAAiC;YACjC,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAElE,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,sBAAsB,CAC1B,uCAAuC,EACvC,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC;CACF;AAED,mCAAmC;AACnC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,+BAA+B;IAC/B,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACjE,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,IAAI,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAExE,2BAA2B;IAC3B,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC9B,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAC9D,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;QAC/B,OAAO,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAC/D,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,SAAS,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QAChC,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC"}