#!/usr/bin/env node
/**
 * Powersteer MCP Server CLI Entry Point
 *
 * This provides a dedicated CLI for starting the MCP server,
 * making it easy to use with npx or as a standalone command.
 */
import { PowersteerMCPServer } from './mcp-server.js';
import { SessionManager } from '../session/session-manager.js';
import { MessageQueue } from '../session/message-queue.js';
import { defaultConfig } from '../server.js';
/**
 * Display help information
 */
function showHelp() {
    console.error(`
Powersteer MCP Server

USAGE:
  powersteer-mcp [OPTIONS]
  npx @powersteer/server mcp [OPTIONS]

DESCRIPTION:
  Starts the Powersteer MCP server for AI agent communication.
  AI agents connect to this server using MCP client libraries.
  Humans use the separate Powersteer CLI to respond to agent requests.

OPTIONS:
  -h, --help     Show this help message
  -v, --version  Show version information
  --debug        Enable debug logging

EXAMPLES:
  # Start MCP server
  powersteer-mcp

  # Start with debug logging
  powersteer-mcp --debug

  # Using npx
  npx @powersteer/server mcp

ARCHITECTURE:
  AI Agent → MCP Client → Powersteer MCP Server → WebSocket → Human CLI

For more information, visit: https://github.com/your-org/powersteer
`);
}
/**
 * Display version information
 */
function showVersion() {
    // Try to read version from package.json
    try {
        const packageJson = JSON.parse(require('fs').readFileSync(new URL('../../package.json', import.meta.url), 'utf8'));
        console.error(`Powersteer MCP Server v${packageJson.version}`);
    }
    catch {
        console.error('Powersteer MCP Server v1.0.0');
    }
}
/**
 * Parse command line arguments
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const config = {
        help: false,
        version: false,
        debug: false
    };
    for (const arg of args) {
        switch (arg) {
            case '-h':
            case '--help':
                config.help = true;
                break;
            case '-v':
            case '--version':
                config.version = true;
                break;
            case '--debug':
                config.debug = true;
                break;
            default:
                console.error(`Unknown option: ${arg}`);
                console.error('Use --help for usage information');
                process.exit(1);
        }
    }
    return config;
}
/**
 * Main function
 */
async function main() {
    const config = parseArgs();
    if (config.help) {
        showHelp();
        return;
    }
    if (config.version) {
        showVersion();
        return;
    }
    // Enable debug logging if requested
    if (config.debug) {
        process.env.DEBUG = 'powersteer:mcp:*';
    }
    console.error('🚀 Starting Powersteer MCP Server...');
    console.error('📡 AI agents can now connect via MCP protocol');
    console.error('👤 Humans should use: npm run start:cli');
    console.error('');
    // Create required dependencies
    const sessionManager = new SessionManager(defaultConfig.session);
    const messageQueue = new MessageQueue(defaultConfig.messageQueue);
    const mcpServer = new PowersteerMCPServer(sessionManager, messageQueue);
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.error('\n🛑 Received SIGINT, shutting down gracefully...');
        await mcpServer.stop();
        process.exit(0);
    });
    process.on('SIGTERM', async () => {
        console.error('\n🛑 Received SIGTERM, shutting down gracefully...');
        await mcpServer.stop();
        process.exit(0);
    });
    // Handle uncaught errors
    process.on('uncaughtException', async (error) => {
        console.error('❌ Uncaught exception:', error);
        await mcpServer.stop();
        process.exit(1);
    });
    process.on('unhandledRejection', async (reason) => {
        console.error('❌ Unhandled rejection:', reason);
        await mcpServer.stop();
        process.exit(1);
    });
    // Start the server
    try {
        await mcpServer.start();
        console.error('✅ MCP Server is running and ready for agent connections');
    }
    catch (error) {
        console.error('❌ Failed to start MCP server:', error);
        process.exit(1);
    }
}
// Run main function
main().catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=mcp-cli.js.map