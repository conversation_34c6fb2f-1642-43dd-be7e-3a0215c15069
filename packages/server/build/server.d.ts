import { SessionConfig } from './session/session-manager.js';
import { MessageQueue, MessageQueueConfig } from './session/message-queue.js';
import { PowersteerWebSocketServer, WebSocketServerConfig } from './websocket/websocket-server.js';
/**
 * Main server configuration
 */
export interface ServerConfig {
    session: SessionConfig;
    messageQueue: MessageQueueConfig;
    webSocket: WebSocketServerConfig;
}
/**
 * Default server configuration
 */
export declare const defaultConfig: ServerConfig;
/**
 * Main Powersteer server that coordinates all components
 */
export declare class PowersteerServer {
    private config;
    private sessionManager;
    private messageQueue;
    private mcpServer;
    private webSocketServer;
    private cleanupTimer?;
    constructor(config?: ServerConfig);
    /**
     * Start all server components
     */
    start: () => Promise<void>;
    /**
     * Stop all server components
     */
    stop: () => Promise<void>;
    /**
     * Get server statistics
     */
    getStats(): {
        sessions: number;
        webSocket: ReturnType<PowersteerWebSocketServer['getStats']>;
        messageQueue: ReturnType<MessageQueue['getQueueStats']>;
    };
    /**
     * Setup periodic cleanup
     */
    private setupCleanupTimer;
    /**
     * Setup global error handlers
     */
    private setupErrorHandlers;
    /**
     * Setup error recovery system
     */
    private setupErrorRecovery;
}
//# sourceMappingURL=server.d.ts.map