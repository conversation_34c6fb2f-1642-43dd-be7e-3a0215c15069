import { AgentRequest, HumanResponse } from '@powersteer/common';
/**
 * Message queue entry
 */
interface QueueEntry<T> {
    id: string;
    data: T;
    timestamp: string;
    processed: boolean;
}
/**
 * Message queue configuration
 */
export interface MessageQueueConfig {
    maxQueueSize: number;
    messageTimeout: number;
}
/**
 * Per-session message queue for handling async agent-human communication
 */
export declare class MessageQueue {
    private config;
    private agentRequests;
    private humanResponses;
    private pendingRequests;
    constructor(config: MessageQueueConfig);
    /**
     * Add agent request to queue and wait for human response
     */
    enqueueAgentRequest: (sessionId: string, request: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }) => Promise<{
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }>;
    /**
     * Add human response and resolve pending request
     */
    enqueueHumanResponse: (sessionId: string, response: {
        sessionId: string;
        timestamp: string;
        response: string;
        approved?: boolean | undefined;
        data?: Record<string, unknown> | undefined;
        systemPrompt?: string | undefined;
        userPrompt?: string | undefined;
    }) => Promise<void>;
    /**
     * Get pending agent requests for a session
     */
    getPendingRequests: (sessionId: string) => Promise<{
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }[]>;
    /**
     * Get message history for a session
     */
    getMessageHistory: (sessionId: string) => Promise<{
        requests: QueueEntry<AgentRequest>[];
        responses: QueueEntry<HumanResponse>[];
    }>;
    /**
     * Clear all messages for a session
     */
    clearSession: (sessionId: string) => Promise<void>;
    /**
     * Get queue statistics
     */
    getQueueStats(): {
        totalSessions: number;
        totalPendingRequests: number;
        totalMessages: number;
    };
    /**
     * Cleanup expired messages and timeouts
     */
    cleanup(): void;
    /**
     * Destroy the message queue and clean up all resources
     */
    destroy(): void;
}
export {};
//# sourceMappingURL=message-queue.d.ts.map