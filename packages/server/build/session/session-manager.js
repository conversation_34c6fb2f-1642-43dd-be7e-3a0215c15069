import { v4 as uuidv4 } from 'uuid';
import { createTimestamp, MCPError, createSessionNotFoundError, withErrorHandling, } from '@powersteer/common';
/**
 * Session manager for handling multiple concurrent agent sessions
 */
export class SessionManager {
    config;
    sessions = new Map();
    timeouts = new Map();
    cleanupTimer;
    constructor(config) {
        this.config = config;
        this.startCleanupTimer();
    }
    /**
     * Create a new session for an agent
     */
    createSession = withErrorHandling(async (agentId) => {
        if (this.sessions.size >= this.config.maxSessions) {
            throw new MCPError('INTERNAL_ERROR', 'Maximum number of sessions reached', { maxSessions: this.config.maxSessions });
        }
        const sessionId = uuidv4();
        const now = createTimestamp();
        const session = {
            id: sessionId,
            status: 'active',
            agentId,
            createdAt: now,
            lastActivity: now,
            timeout: this.config.defaultTimeout,
        };
        this.sessions.set(sessionId, session);
        this.setSessionTimeout(sessionId);
        return session;
    });
    /**
     * Create a new session with a specific ID (for CLI-generated sessions)
     */
    createSessionWithId = withErrorHandling(async (sessionId, agentId) => {
        if (this.sessions.size >= this.config.maxSessions) {
            throw new MCPError('INTERNAL_ERROR', 'Maximum number of sessions reached', { maxSessions: this.config.maxSessions });
        }
        if (this.sessions.has(sessionId)) {
            throw new MCPError('INTERNAL_ERROR', 'Session with this ID already exists', { sessionId });
        }
        const now = createTimestamp();
        const session = {
            id: sessionId,
            status: 'active',
            agentId,
            createdAt: now,
            lastActivity: now,
            timeout: this.config.defaultTimeout,
        };
        this.sessions.set(sessionId, session);
        this.setSessionTimeout(sessionId);
        return session;
    });
    /**
     * Get session by ID
     */
    getSession = withErrorHandling(async (sessionId) => {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw createSessionNotFoundError(sessionId);
        }
        return { ...session };
    });
    /**
     * List all sessions
     */
    listSessions = withErrorHandling(async () => {
        return Array.from(this.sessions.values()).map(session => ({ ...session }));
    });
    /**
     * Update session status
     */
    updateSessionStatus = withErrorHandling(async (sessionId, status, currentRequest) => {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw createSessionNotFoundError(sessionId);
        }
        const updatedSession = {
            ...session,
            status,
            lastActivity: createTimestamp(),
            currentRequest,
        };
        this.sessions.set(sessionId, updatedSession);
        // Reset timeout for active sessions
        if (status === 'active' || status === 'waiting') {
            this.setSessionTimeout(sessionId);
        }
        else {
            this.clearSessionTimeout(sessionId);
        }
        return { ...updatedSession };
    });
    /**
     * Set current request for a session
     */
    setCurrentRequest = withErrorHandling(async (sessionId, request) => {
        return this.updateSessionStatus(sessionId, 'waiting', request);
    });
    /**
     * Clear current request for a session
     */
    clearCurrentRequest = withErrorHandling(async (sessionId) => {
        return this.updateSessionStatus(sessionId, 'active');
    });
    /**
     * Remove session
     */
    removeSession = withErrorHandling(async (sessionId) => {
        this.clearSessionTimeout(sessionId);
        this.sessions.delete(sessionId);
    });
    /**
     * Get all active sessions
     */
    getActiveSessions = withErrorHandling(async () => {
        return Array.from(this.sessions.values())
            .filter(session => session.status === 'active' || session.status === 'waiting')
            .map(session => ({ ...session }));
    });
    /**
     * Get session count
     */
    getSessionCount() {
        return this.sessions.size;
    }
    /**
     * Check if session exists
     */
    hasSession(sessionId) {
        return this.sessions.has(sessionId);
    }
    /**
     * Cleanup expired sessions
     */
    cleanupExpiredSessions() {
        const now = Date.now();
        const expiredSessions = [];
        for (const [sessionId, session] of this.sessions.entries()) {
            const lastActivity = new Date(session.lastActivity).getTime();
            const timeoutMs = session.timeout * 1000;
            if (now - lastActivity > timeoutMs) {
                expiredSessions.push(sessionId);
            }
        }
        for (const sessionId of expiredSessions) {
            this.handleSessionTimeout(sessionId);
        }
    }
    /**
     * Handle session timeout
     */
    handleSessionTimeout(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            // Update status to timeout
            this.sessions.set(sessionId, {
                ...session,
                status: 'timeout',
                lastActivity: createTimestamp(),
            });
            // Clear timeout and remove after a grace period
            this.clearSessionTimeout(sessionId);
            setTimeout(() => {
                this.sessions.delete(sessionId);
            }, 30000); // 30 second grace period
        }
    }
    /**
     * Set session timeout
     */
    setSessionTimeout(sessionId) {
        this.clearSessionTimeout(sessionId);
        const session = this.sessions.get(sessionId);
        if (session) {
            const timeout = setTimeout(() => {
                this.handleSessionTimeout(sessionId);
            }, session.timeout * 1000);
            this.timeouts.set(sessionId, timeout);
        }
    }
    /**
     * Clear session timeout
     */
    clearSessionTimeout(sessionId) {
        const timeout = this.timeouts.get(sessionId);
        if (timeout) {
            clearTimeout(timeout);
            this.timeouts.delete(sessionId);
        }
    }
    /**
     * Start cleanup timer
     */
    startCleanupTimer() {
        this.cleanupTimer = setInterval(() => {
            this.cleanupExpiredSessions();
        }, this.config.cleanupInterval);
    }
    /**
     * Stop cleanup timer and clear all sessions
     */
    destroy() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        // Clear all timeouts
        for (const timeout of this.timeouts.values()) {
            clearTimeout(timeout);
        }
        this.timeouts.clear();
        this.sessions.clear();
    }
}
//# sourceMappingURL=session-manager.js.map