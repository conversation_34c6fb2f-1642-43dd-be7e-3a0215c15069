import { SessionId } from '@powersteer/common';
/**
 * Session configuration options
 */
export interface SessionConfig {
    defaultTimeout: number;
    maxSessions: number;
    cleanupInterval: number;
}
/**
 * Session manager for handling multiple concurrent agent sessions
 */
export declare class SessionManager {
    private config;
    private sessions;
    private timeouts;
    private cleanupTimer?;
    constructor(config: SessionConfig);
    /**
     * Create a new session for an agent
     */
    createSession: (agentId: string) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * Create a new session with a specific ID (for CLI-generated sessions)
     */
    createSessionWithId: (sessionId: string, agentId: string) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * Get session by ID
     */
    getSession: (sessionId: string) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * List all sessions
     */
    listSessions: () => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }[]>;
    /**
     * Update session status
     */
    updateSessionStatus: (sessionId: string, status: "timeout" | "active" | "waiting" | "completed" | "error", currentRequest?: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    } | undefined) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * Set current request for a session
     */
    setCurrentRequest: (sessionId: string, request: {
        sessionId: string;
        type: "confirmation" | "information" | "decision" | "approval" | "feedback";
        priority: "low" | "normal" | "high" | "urgent";
        summary: string;
        timeout: number;
        timestamp: string;
        details?: string | undefined;
        context?: Record<string, unknown> | undefined;
    }) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * Clear current request for a session
     */
    clearCurrentRequest: (sessionId: string) => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }>;
    /**
     * Remove session
     */
    removeSession: (sessionId: string) => Promise<void>;
    /**
     * Get all active sessions
     */
    getActiveSessions: () => Promise<{
        status: "timeout" | "active" | "waiting" | "completed" | "error";
        timeout: number;
        id: string;
        agentId: string;
        createdAt: string;
        lastActivity: string;
        currentRequest?: {
            sessionId: string;
            type: "confirmation" | "information" | "decision" | "approval" | "feedback";
            priority: "low" | "normal" | "high" | "urgent";
            summary: string;
            timeout: number;
            timestamp: string;
            details?: string | undefined;
            context?: Record<string, unknown> | undefined;
        } | undefined;
    }[]>;
    /**
     * Get session count
     */
    getSessionCount(): number;
    /**
     * Check if session exists
     */
    hasSession(sessionId: SessionId): boolean;
    /**
     * Cleanup expired sessions
     */
    private cleanupExpiredSessions;
    /**
     * Handle session timeout
     */
    private handleSessionTimeout;
    /**
     * Set session timeout
     */
    private setSessionTimeout;
    /**
     * Clear session timeout
     */
    private clearSessionTimeout;
    /**
     * Start cleanup timer
     */
    private startCleanupTimer;
    /**
     * Stop cleanup timer and clear all sessions
     */
    destroy(): void;
}
//# sourceMappingURL=session-manager.d.ts.map