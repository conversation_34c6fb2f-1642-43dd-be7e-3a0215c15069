{"version": 3, "file": "session-manager.js", "sourceRoot": "", "sources": ["../../src/session/session-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AACpC,OAAO,EAML,eAAe,EACf,QAAQ,EACR,0BAA0B,EAE1B,iBAAiB,GAClB,MAAM,oBAAoB,CAAC;AAW5B;;GAEG;AACH,MAAM,OAAO,cAAc;IAKL;IAJZ,QAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;IACzC,QAAQ,GAAG,IAAI,GAAG,EAA6B,CAAC;IAChD,YAAY,CAAkB;IAEtC,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;QACvC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAe,EAAoB,EAAE;QAC5E,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,IAAI,QAAQ,CAChB,gBAAgB,EAChB,oCAAoC,EACpC,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CACzC,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,EAAe,CAAC;QACxC,MAAM,GAAG,GAAG,eAAe,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,SAAS;YACb,MAAM,EAAE,QAAQ;YAChB,OAAO;YACP,SAAS,EAAE,GAAG;YACd,YAAY,EAAE,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;SACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAElC,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,mBAAmB,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAoB,EAAE,OAAe,EAAoB,EAAE;QACxG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,IAAI,QAAQ,CAChB,gBAAgB,EAChB,oCAAoC,EACpC,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CACzC,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,QAAQ,CAChB,gBAAgB,EAChB,qCAAqC,EACrC,EAAE,SAAS,EAAE,CACd,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,eAAe,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,SAAS;YACb,MAAM,EAAE,QAAQ;YAChB,OAAO;YACP,SAAS,EAAE,GAAG;YACd,YAAY,EAAE,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;SACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAElC,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,UAAU,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAoB,EAAoB,EAAE;QAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,0BAA0B,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,YAAY,GAAG,iBAAiB,CAAC,KAAK,IAAwB,EAAE;QAC9D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,mBAAmB,GAAG,iBAAiB,CAAC,KAAK,EAC3C,SAAoB,EACpB,MAAqB,EACrB,cAA6B,EACX,EAAE;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,0BAA0B,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,cAAc,GAAY;YAC9B,GAAG,OAAO;YACV,MAAM;YACN,YAAY,EAAE,eAAe,EAAE;YAC/B,cAAc;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAE7C,oCAAoC;QACpC,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAChD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,EACzC,SAAoB,EACpB,OAAqB,EACH,EAAE;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,mBAAmB,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAoB,EAAoB,EAAE;QACvF,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAoB,EAAiB,EAAE;QAC9E,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,IAAwB,EAAE;QACnE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC;aAC9E,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAoB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,eAAe,GAAgB,EAAE,CAAC;QAExC,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;YAC9D,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAEzC,IAAI,GAAG,GAAG,YAAY,GAAG,SAAS,EAAE,CAAC;gBACnC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,SAAoB;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC3B,GAAG,OAAO;gBACV,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,eAAe,EAAE;aAChC,CAAC,CAAC;YAEH,gDAAgD;YAChD,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAoB;QAC5C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;YAE3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,SAAoB;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,YAAY,CAAC,OAAO,CAAC,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;CACF"}