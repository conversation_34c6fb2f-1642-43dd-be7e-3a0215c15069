{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAgB,MAAM,aAAa,CAAC;AAE5E;;GAEG;AACH,SAAS,SAAS;IAChB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,MAAM,GAA0B,EAAE,CAAC;IAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE5B,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,IAAI;gBACP,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBACvC,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;oBACzE,CAAC,EAAE,CAAC;gBACN,CAAC;gBACD,MAAM;YAER,KAAK,gBAAgB;gBACnB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBACvC,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC5E,CAAC,EAAE,CAAC;gBACN,CAAC;gBACD,MAAM;YAER,KAAK,mBAAmB;gBACtB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBACvC,MAAM,CAAC,OAAO,GAAG;wBACf,GAAG,aAAa,CAAC,OAAO;wBACxB,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC;qBAChC,CAAC;oBACF,CAAC,EAAE,CAAC;gBACN,CAAC;gBACD,MAAM;YAER,KAAK,mBAAmB;gBACtB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBACvC,MAAM,CAAC,YAAY,GAAG;wBACpB,GAAG,aAAa,CAAC,YAAY;wBAC7B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC;qBAChC,CAAC;oBACF,CAAC,EAAE,CAAC;gBACN,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ,CAAC;YACd,KAAK,IAAI;gBACP,SAAS,EAAE,CAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChB,MAAM;YAER,KAAK,WAAW,CAAC;YACjB,KAAK,IAAI;gBACP,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChB,MAAM;YAER;gBACE,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxB,OAAO,CAAC,KAAK,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC;oBACxC,SAAS,EAAE,CAAC;oBACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,SAAS;IAChB,OAAO,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+Bf,CAAC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,YAAY;IACnB,MAAM,MAAM,GAA0B,EAAE,CAAC;IAEzC,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACjB,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC;QACxC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC/D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,OAAO,GAAG;gBACf,GAAG,aAAa,CAAC,OAAO;gBACxB,cAAc,EAAE,OAAO;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC/D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,YAAY,GAAG;gBACpB,GAAG,aAAa,CAAC,YAAY;gBAC7B,cAAc,EAAE,OAAO;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QACH,wEAAwE;QACxE,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC;QAE9B,MAAM,WAAW,GAAiB;YAChC,OAAO,EAAE;gBACP,GAAG,aAAa,CAAC,OAAO;gBACxB,GAAG,SAAS,CAAC,OAAO;gBACpB,GAAG,SAAS,CAAC,OAAO;aACrB;YACD,YAAY,EAAE;gBACZ,GAAG,aAAa,CAAC,YAAY;gBAC7B,GAAG,SAAS,CAAC,YAAY;gBACzB,GAAG,SAAS,CAAC,YAAY;aAC1B;YACD,SAAS,EAAE;gBACT,GAAG,aAAa,CAAC,SAAS;gBAC1B,GAAG,SAAS,CAAC,SAAS;gBACtB,GAAG,SAAS,CAAC,SAAS;aACvB;SACF,CAAC;QAEF,0BAA0B;QAC1B,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACjD,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QAErB,2BAA2B;QAC3B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,iBAAiB;AACjB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}