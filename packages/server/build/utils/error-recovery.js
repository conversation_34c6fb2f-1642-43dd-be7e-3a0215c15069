import { createInternalError, withErrorHandling, withRetry } from '@powersteer/common';
/**
 * Default error recovery configuration
 */
export const defaultErrorRecoveryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    circuitBreakerThreshold: 5,
    circuitBreakerTimeout: 30000,
    healthCheckInterval: 10000,
};
/**
 * Circuit breaker for preventing cascade failures
 */
export class CircuitBreaker {
    config;
    state = 'closed';
    failureCount = 0;
    lastFailureTime = 0;
    successCount = 0;
    constructor(config) {
        this.config = config;
    }
    /**
     * Execute function with circuit breaker protection
     */
    async execute(fn) {
        if (this.state === 'open') {
            if (Date.now() - this.lastFailureTime > this.config.circuitBreakerTimeout) {
                this.state = 'half-open';
                this.successCount = 0;
            }
            else {
                throw createInternalError('Circuit breaker is open - service unavailable');
            }
        }
        try {
            const result = await fn();
            this.onSuccess();
            return result;
        }
        catch (error) {
            this.onFailure();
            throw error;
        }
    }
    onSuccess() {
        this.failureCount = 0;
        if (this.state === 'half-open') {
            this.successCount++;
            if (this.successCount >= 3) {
                this.state = 'closed';
            }
        }
    }
    onFailure() {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        if (this.failureCount >= this.config.circuitBreakerThreshold) {
            this.state = 'open';
        }
    }
    getState() {
        return this.state;
    }
    getFailureCount() {
        return this.failureCount;
    }
}
/**
 * Server error recovery manager
 */
export class ServerErrorRecovery {
    config;
    circuitBreakers = new Map();
    healthCheckTimer;
    isShuttingDown = false;
    constructor(config = defaultErrorRecoveryConfig) {
        this.config = config;
        this.startHealthCheck();
    }
    /**
     * Get or create circuit breaker for a service
     */
    getCircuitBreaker(serviceName) {
        if (!this.circuitBreakers.has(serviceName)) {
            this.circuitBreakers.set(serviceName, new CircuitBreaker(this.config));
        }
        return this.circuitBreakers.get(serviceName);
    }
    /**
     * Handle WebSocket connection errors with recovery
     */
    handleWebSocketError = withErrorHandling(async (error, ws) => {
        console.error('WebSocket error occurred:', error);
        // Attempt graceful recovery
        try {
            if (ws && ws.readyState === 1) { // WebSocket.OPEN
                ws.send(JSON.stringify({
                    type: 'error',
                    error: {
                        code: 'WEBSOCKET_ERROR',
                        message: 'Connection error occurred, attempting recovery',
                    },
                    timestamp: new Date().toISOString(),
                }));
            }
        }
        catch (recoveryError) {
            console.error('Failed to send error recovery message:', recoveryError);
        }
        // Log error for monitoring
        this.logError('websocket', error);
    });
    /**
     * Handle session timeout with recovery
     */
    handleSessionTimeout = withErrorHandling(async (sessionId, sessionManager) => {
        console.warn(`Session ${sessionId} timed out, attempting recovery...`);
        try {
            // Attempt to extend session if possible
            const session = sessionManager.getSession(sessionId);
            if (session && session.status === 'active') {
                await sessionManager.extendSession(sessionId, 300); // Extend by 5 minutes
                console.error(`Session ${sessionId} extended successfully`);
                return true;
            }
        }
        catch (error) {
            console.error(`Failed to recover session ${sessionId}:`, error);
        }
        // Clean up session resources
        try {
            await sessionManager.cleanupSession(sessionId);
        }
        catch (cleanupError) {
            console.error(`Failed to cleanup session ${sessionId}:`, cleanupError);
        }
        return false;
    });
    /**
     * Handle MCP protocol errors with recovery
     */
    handleMCPError = withErrorHandling(async (error, context) => {
        console.error('MCP protocol error:', error);
        // Attempt protocol-specific recovery
        switch (error.code) {
            case 'COMMUNICATION_ERROR':
                return this.recoverCommunicationError(error, context);
            case 'WEBSOCKET_ERROR':
                return this.recoverWebSocketError(error, context);
            case 'SESSION_NOT_FOUND':
                return this.recoverSessionError(error, context);
            default:
                console.warn(`No specific recovery for error code: ${error.code}`);
                return false;
        }
    });
    /**
     * Recover from communication errors
     */
    async recoverCommunicationError(error, context) {
        console.log('Attempting communication error recovery...');
        // Implement retry with exponential backoff
        return withRetry(async () => {
            // Attempt to re-establish communication
            if (context.reconnect) {
                await context.reconnect();
                return true;
            }
            throw error;
        }, {
            maxRetries: this.config.maxRetries,
            retryDelay: this.config.retryDelay,
        }).catch(() => false);
    }
    /**
     * Recover from WebSocket errors
     */
    async recoverWebSocketError(error, context) {
        console.log('Attempting WebSocket error recovery...');
        try {
            if (context.ws && context.ws.readyState !== 1) {
                // WebSocket is not open, attempt reconnection
                if (context.reconnect) {
                    await context.reconnect();
                    return true;
                }
            }
        }
        catch (recoveryError) {
            console.error('WebSocket recovery failed:', recoveryError);
        }
        return false;
    }
    /**
     * Recover from session errors
     */
    async recoverSessionError(error, context) {
        console.log('Attempting session error recovery...');
        try {
            if (context.sessionManager && context.sessionId) {
                // Attempt to recreate session
                const newSession = await context.sessionManager.createSession(context.agentId || 'recovered-agent');
                if (context.onSessionRecovered) {
                    context.onSessionRecovered(newSession);
                }
                return true;
            }
        }
        catch (recoveryError) {
            console.error('Session recovery failed:', recoveryError);
        }
        return false;
    }
    /**
     * Start health check monitoring
     */
    startHealthCheck() {
        this.healthCheckTimer = setInterval(() => {
            if (this.isShuttingDown)
                return;
            this.performHealthCheck();
        }, this.config.healthCheckInterval);
    }
    /**
     * Perform system health check
     */
    performHealthCheck() {
        console.error('Performing system health check...');
        // Check circuit breaker states
        for (const [serviceName, circuitBreaker] of this.circuitBreakers) {
            const state = circuitBreaker.getState();
            const failureCount = circuitBreaker.getFailureCount();
            if (state === 'open') {
                console.error(`Service ${serviceName} circuit breaker is OPEN (failures: ${failureCount})`);
            }
            else if (failureCount > 0) {
                console.error(`Service ${serviceName} has ${failureCount} recent failures`);
            }
        }
    }
    /**
     * Log error for monitoring and analysis
     */
    logError(category, error) {
        const errorLog = {
            timestamp: new Date().toISOString(),
            category,
            message: error.message,
            stack: error.stack,
            name: error.name,
        };
        // In production, this would send to monitoring service
        console.error('Error logged:', JSON.stringify(errorLog, null, 2));
    }
    /**
     * Graceful shutdown
     */
    async shutdown() {
        console.error('Shutting down error recovery system...');
        this.isShuttingDown = true;
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        // Close all circuit breakers
        this.circuitBreakers.clear();
    }
}
/**
 * Global error recovery instance
 */
export const serverErrorRecovery = new ServerErrorRecovery();
/**
 * Graceful shutdown handler
 */
export function setupGracefulShutdown() {
    const shutdown = async (signal) => {
        console.error(`Received ${signal}, initiating graceful shutdown...`);
        try {
            await serverErrorRecovery.shutdown();
            console.error('Graceful shutdown completed');
            process.exit(0);
        }
        catch (error) {
            console.error('Error during shutdown:', error);
            process.exit(1);
        }
    };
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
}
//# sourceMappingURL=error-recovery.js.map