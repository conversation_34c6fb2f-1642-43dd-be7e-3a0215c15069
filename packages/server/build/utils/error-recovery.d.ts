import { MCPError } from '@powersteer/common';
/**
 * Server error recovery configuration
 */
export interface ErrorRecoveryConfig {
    maxRetries: number;
    retryDelay: number;
    circuitBreakerThreshold: number;
    circuitBreakerTimeout: number;
    healthCheckInterval: number;
}
/**
 * Default error recovery configuration
 */
export declare const defaultErrorRecoveryConfig: ErrorRecoveryConfig;
/**
 * Circuit breaker states
 */
type CircuitBreakerState = 'closed' | 'open' | 'half-open';
/**
 * Circuit breaker for preventing cascade failures
 */
export declare class CircuitBreaker {
    private config;
    private state;
    private failureCount;
    private lastFailureTime;
    private successCount;
    constructor(config: ErrorRecoveryConfig);
    /**
     * Execute function with circuit breaker protection
     */
    execute<T>(fn: () => Promise<T>): Promise<T>;
    private onSuccess;
    private onFailure;
    getState(): CircuitBreakerState;
    getFailureCount(): number;
}
/**
 * Server error recovery manager
 */
export declare class ServerErrorRecovery {
    private config;
    private circuitBreakers;
    private healthCheckTimer?;
    private isShuttingDown;
    constructor(config?: ErrorRecoveryConfig);
    /**
     * Get or create circuit breaker for a service
     */
    getCircuitBreaker(serviceName: string): CircuitBreaker;
    /**
     * Handle WebSocket connection errors with recovery
     */
    handleWebSocketError: (error: Error, ws: any) => Promise<void>;
    /**
     * Handle session timeout with recovery
     */
    handleSessionTimeout: (sessionId: string, sessionManager: any) => Promise<boolean>;
    /**
     * Handle MCP protocol errors with recovery
     */
    handleMCPError: (error: MCPError, context: any) => Promise<boolean>;
    /**
     * Recover from communication errors
     */
    private recoverCommunicationError;
    /**
     * Recover from WebSocket errors
     */
    private recoverWebSocketError;
    /**
     * Recover from session errors
     */
    private recoverSessionError;
    /**
     * Start health check monitoring
     */
    private startHealthCheck;
    /**
     * Perform system health check
     */
    private performHealthCheck;
    /**
     * Log error for monitoring and analysis
     */
    private logError;
    /**
     * Graceful shutdown
     */
    shutdown(): Promise<void>;
}
/**
 * Global error recovery instance
 */
export declare const serverErrorRecovery: ServerErrorRecovery;
/**
 * Graceful shutdown handler
 */
export declare function setupGracefulShutdown(): void;
export {};
//# sourceMappingURL=error-recovery.d.ts.map