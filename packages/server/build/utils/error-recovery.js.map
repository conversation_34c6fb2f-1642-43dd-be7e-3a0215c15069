{"version": 3, "file": "error-recovery.js", "sourceRoot": "", "sources": ["../../src/utils/error-recovery.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,mBAAmB,EAEnB,iBAAiB,EACjB,SAAS,EACV,MAAM,oBAAoB,CAAC;AAa5B;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAwB;IAC7D,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,IAAI;IAChB,uBAAuB,EAAE,CAAC;IAC1B,qBAAqB,EAAE,KAAK;IAC5B,mBAAmB,EAAE,KAAK;CAC3B,CAAC;AAOF;;GAEG;AACH,MAAM,OAAO,cAAc;IAML;IALZ,KAAK,GAAwB,QAAQ,CAAC;IACtC,YAAY,GAAG,CAAC,CAAC;IACjB,eAAe,GAAG,CAAC,CAAC;IACpB,YAAY,GAAG,CAAC,CAAC;IAEzB,YAAoB,MAA2B;QAA3B,WAAM,GAAN,MAAM,CAAqB;IAAG,CAAC;IAEnD;;OAEG;IACH,KAAK,CAAC,OAAO,CAAI,EAAoB;QACnC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;gBAC1E,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;gBACzB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,mBAAmB,CAAC,+CAA+C,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YAC7D,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACtB,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAKV;IAJZ,eAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;IACpD,gBAAgB,CAAkB;IAClC,cAAc,GAAG,KAAK,CAAC;IAE/B,YAAoB,SAA8B,0BAA0B;QAAxD,WAAM,GAAN,MAAM,CAAkD;QAC1E,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,WAAmB;QACnC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAY,EAAE,EAAO,EAAE,EAAE;QACvE,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAElD,4BAA4B;QAC5B,IAAI,CAAC;YACH,IAAI,EAAE,IAAI,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB;gBAChD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrB,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,iBAAiB;wBACvB,OAAO,EAAE,gDAAgD;qBAC1D;oBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,aAAa,CAAC,CAAC;QACzE,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAiB,EAAE,cAAmB,EAAE,EAAE;QACxF,OAAO,CAAC,IAAI,CAAC,WAAW,SAAS,oCAAoC,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC3C,MAAM,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,sBAAsB;gBAC1E,OAAO,CAAC,KAAK,CAAC,WAAW,SAAS,wBAAwB,CAAC,CAAC;gBAC5D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC;YACH,MAAM,cAAc,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,6BAA6B,SAAS,GAAG,EAAE,YAAY,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,cAAc,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAe,EAAE,OAAY,EAAE,EAAE;QACzE,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAE5C,qCAAqC;QACrC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACxD,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACpD,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClD;gBACE,OAAO,CAAC,IAAI,CAAC,wCAAwC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACnE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACK,KAAK,CAAC,yBAAyB,CAAC,KAAe,EAAE,OAAY;QACnE,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,2CAA2C;QAC3C,OAAO,SAAS,CAAC,KAAK,IAAI,EAAE;YAC1B,wCAAwC;YACxC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC,EAAE;YACD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;SACnC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,KAAe,EAAE,OAAY;QAC/D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBAC9C,8CAA8C;gBAC9C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;oBACtB,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;oBAC1B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,KAAe,EAAE,OAAY;QAC7D,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBAChD,8BAA8B;gBAC9B,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,IAAI,iBAAiB,CAAC,CAAC;gBACpG,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBACzC,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,IAAI,IAAI,CAAC,cAAc;gBAAE,OAAO;YAEhC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAEnD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,YAAY,GAAG,cAAc,CAAC,eAAe,EAAE,CAAC;YAEtD,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,WAAW,WAAW,uCAAuC,YAAY,GAAG,CAAC,CAAC;YAC9F,CAAC;iBAAM,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,KAAK,CAAC,WAAW,WAAW,QAAQ,YAAY,kBAAkB,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,QAAgB,EAAE,KAAY;QAC7C,MAAM,QAAQ,GAAG;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ;YACR,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB,CAAC;QAEF,uDAAuD;QACvD,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvC,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAE7D;;GAEG;AACH,MAAM,UAAU,qBAAqB;IACnC,MAAM,QAAQ,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;QACxC,OAAO,CAAC,KAAK,CAAC,YAAY,MAAM,mCAAmC,CAAC,CAAC;QAErE,IAAI,CAAC;YACH,MAAM,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IACjD,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc;AAClE,CAAC"}