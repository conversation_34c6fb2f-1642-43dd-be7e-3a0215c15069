#!/usr/bin/env node
import { PowersteerServer, defaultConfig } from './server.js';
/**
 * Parse command line arguments
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const config = {};
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        const nextArg = args[i + 1];
        switch (arg) {
            case '--port':
            case '-p':
                if (nextArg && !isNaN(Number(nextArg))) {
                    config.webSocket = { ...defaultConfig.webSocket, port: Number(nextArg) };
                    i++;
                }
                break;
            case '--max-sessions':
                if (nextArg && !isNaN(Number(nextArg))) {
                    config.session = { ...defaultConfig.session, maxSessions: Number(nextArg) };
                    i++;
                }
                break;
            case '--session-timeout':
                if (nextArg && !isNaN(Number(nextArg))) {
                    config.session = {
                        ...defaultConfig.session,
                        defaultTimeout: Number(nextArg)
                    };
                    i++;
                }
                break;
            case '--message-timeout':
                if (nextArg && !isNaN(Number(nextArg))) {
                    config.messageQueue = {
                        ...defaultConfig.messageQueue,
                        messageTimeout: Number(nextArg)
                    };
                    i++;
                }
                break;
            case '--help':
            case '-h':
                printHelp();
                process.exit(0);
                break;
            case '--version':
            case '-v':
                console.error('Powersteer Server v1.0.0');
                process.exit(0);
                break;
            default:
                if (arg.startsWith('-')) {
                    console.error(`Unknown option: ${arg}`);
                    printHelp();
                    process.exit(1);
                }
                break;
        }
    }
    return config;
}
/**
 * Print help message
 */
function printHelp() {
    console.error(`
Powersteer Server - MCP server for human-in-the-loop agent communication

Usage: powersteer-server [options]

Options:
  -p, --port <number>           WebSocket server port (default: 8080)
  --max-sessions <number>       Maximum concurrent sessions (default: 100)
  --session-timeout <seconds>   Session timeout in seconds (default: 300)
  --message-timeout <seconds>   Message timeout in seconds (default: 300)
  -h, --help                    Show this help message
  -v, --version                 Show version information

Examples:
  powersteer-server                    # Start with default settings
  powersteer-server -p 9000           # Start on port 9000
  powersteer-server --max-sessions 50 # Limit to 50 concurrent sessions

The server provides:
  - MCP protocol interface for agents (stdio transport)
  - WebSocket interface for CLI clients (port specified by --port)
  - Session management and message queuing
  - Automatic cleanup and timeout handling

Environment Variables:
  POWERSTEER_PORT              WebSocket server port
  POWERSTEER_MAX_SESSIONS      Maximum concurrent sessions
  POWERSTEER_SESSION_TIMEOUT   Session timeout in seconds
  POWERSTEER_MESSAGE_TIMEOUT   Message timeout in seconds

For more information, visit: https://github.com/powersteer/powersteer
`);
}
/**
 * Merge configuration from environment variables
 */
function getEnvConfig() {
    const config = {};
    if (process.env.POWERSTEER_PORT) {
        const port = Number(process.env.POWERSTEER_PORT);
        if (!isNaN(port)) {
            config.webSocket = { ...defaultConfig.webSocket, port };
        }
    }
    if (process.env.POWERSTEER_MAX_SESSIONS) {
        const maxSessions = Number(process.env.POWERSTEER_MAX_SESSIONS);
        if (!isNaN(maxSessions)) {
            config.session = { ...defaultConfig.session, maxSessions };
        }
    }
    if (process.env.POWERSTEER_SESSION_TIMEOUT) {
        const timeout = Number(process.env.POWERSTEER_SESSION_TIMEOUT);
        if (!isNaN(timeout)) {
            config.session = {
                ...defaultConfig.session,
                defaultTimeout: timeout
            };
        }
    }
    if (process.env.POWERSTEER_MESSAGE_TIMEOUT) {
        const timeout = Number(process.env.POWERSTEER_MESSAGE_TIMEOUT);
        if (!isNaN(timeout)) {
            config.messageQueue = {
                ...defaultConfig.messageQueue,
                messageTimeout: timeout
            };
        }
    }
    return config;
}
/**
 * Main entry point
 */
async function main() {
    try {
        // Merge configuration from environment, command line args, and defaults
        const envConfig = getEnvConfig();
        const argConfig = parseArgs();
        const finalConfig = {
            session: {
                ...defaultConfig.session,
                ...envConfig.session,
                ...argConfig.session,
            },
            messageQueue: {
                ...defaultConfig.messageQueue,
                ...envConfig.messageQueue,
                ...argConfig.messageQueue,
            },
            webSocket: {
                ...defaultConfig.webSocket,
                ...envConfig.webSocket,
                ...argConfig.webSocket,
            },
        };
        // Create and start server
        const server = new PowersteerServer(finalConfig);
        await server.start();
        // Keep the process running
        process.stdin.resume();
    }
    catch (error) {
        console.error('Failed to start Powersteer Server:', error);
        process.exit(1);
    }
}
// Run the server
main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=cli.js.map