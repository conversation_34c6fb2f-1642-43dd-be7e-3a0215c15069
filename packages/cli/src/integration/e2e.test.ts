import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import WebSocket from 'ws';
import { WebSocketClient } from '../websocket/WebSocketClient.js';
import { HumanResponse } from '@powersteer/common';

// Mock WebSocket for controlled testing
vi.mock('ws');
const MockWebSocket = vi.mocked(WebSocket);

describe('End-to-End CLI Tests', () => {
  let mockWs: any;
  let client: WebSocketClient;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock WebSocket instance
    mockWs = {
      readyState: WebSocket.OPEN,
      send: vi.fn(),
      close: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    };

    MockWebSocket.mockImplementation(() => mockWs);

    client = new WebSocketClient({
      url: 'ws://localhost:8082',
      reconnectInterval: 1000,
      maxReconnectAttempts: 3,
    });
  });

  afterEach(() => {
    client.disconnect();
  });

  describe('Full Agent-Human Communication Workflow', () => {
    it('should handle complete request-response cycle', async () => {
      const connectionSpy = vi.fn();
      const agentRequestSpy = vi.fn();
      
      client.on('connectionChange', connectionSpy);
      client.on('agentRequest', agentRequestSpy);

      // Start connection
      await client.connect();

      // Simulate WebSocket open event
      const openHandler = mockWs.on.mock.calls.find(call => call[0] === 'open')?.[1];
      if (openHandler) {
        openHandler();
      }

      expect(connectionSpy).toHaveBeenCalledWith('connected');

      // Simulate incoming agent request
      const messageHandler = mockWs.on.mock.calls.find(call => call[0] === 'message')?.[1];
      const agentRequestMessage = JSON.stringify({
        type: 'agent_request',
        data: {
          sessionId: 'test-session-123',
          summary: 'Test agent request',
          context: 'Please help with testing',
          timestamp: new Date().toISOString(),
          timeout: 30000,
        },
      });

      if (messageHandler) {
        messageHandler(agentRequestMessage);
      }

      expect(agentRequestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          sessionId: 'test-session-123',
          summary: 'Test agent request',
          context: 'Please help with testing',
        })
      );

      // Send human response
      const humanResponse: HumanResponse = {
        sessionId: 'test-session-123',
        response: 'This is my response to the agent',
        timestamp: new Date().toISOString(),
      };

      await client.sendHumanResponse(humanResponse);

      expect(mockWs.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'human_response',
          data: humanResponse,
        }),
        expect.any(Function)
      );
    });

    it('should handle connection failures and reconnection', async () => {
      const connectionSpy = vi.fn();
      client.on('connectionChange', connectionSpy);

      await client.connect();

      // Simulate connection error
      const errorHandler = mockWs.on.mock.calls.find(call => call[0] === 'error')?.[1];
      if (errorHandler) {
        errorHandler(new Error('Connection failed'));
      }

      expect(connectionSpy).toHaveBeenCalledWith('error');

      // Simulate connection close (triggers reconnection)
      const closeHandler = mockWs.on.mock.calls.find(call => call[0] === 'close')?.[1];
      if (closeHandler) {
        closeHandler(1006, 'Connection lost');
      }

      expect(connectionSpy).toHaveBeenCalledWith('disconnected');
    });

    it('should handle malformed messages gracefully', async () => {
      const errorSpy = vi.fn();
      client.on('error', errorSpy);

      await client.connect();

      // Simulate malformed message
      const messageHandler = mockWs.on.mock.calls.find(call => call[0] === 'message')?.[1];
      if (messageHandler) {
        messageHandler('invalid json');
      }

      expect(errorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('Failed to parse message'),
        })
      );
    });

    it('should handle unknown message types', async () => {
      await client.connect();

      // Capture console.warn calls
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Simulate unknown message type
      const messageHandler = mockWs.on.mock.calls.find(call => call[0] === 'message')?.[1];
      const unknownMessage = JSON.stringify({
        type: 'unknown_type',
        data: { some: 'data' },
      });

      if (messageHandler) {
        messageHandler(unknownMessage);
      }

      expect(consoleSpy).toHaveBeenCalledWith('Unknown message type:', 'unknown_type');
      
      consoleSpy.mockRestore();
    });

    it('should prevent sending responses when not connected', async () => {
      // Don't connect the client
      const humanResponse: HumanResponse = {
        sessionId: 'test-session-123',
        response: 'This should fail',
        timestamp: new Date().toISOString(),
      };

      await expect(client.sendHumanResponse(humanResponse)).rejects.toThrow(
        'WebSocket not connected'
      );
    });

    it('should handle graceful disconnection', async () => {
      const connectionSpy = vi.fn();
      client.on('connectionChange', connectionSpy);

      await client.connect();

      // Simulate successful connection
      const openHandler = mockWs.on.mock.calls.find(call => call[0] === 'open')?.[1];
      if (openHandler) {
        openHandler();
      }

      expect(connectionSpy).toHaveBeenCalledWith('connected');

      // Disconnect gracefully
      client.disconnect();

      expect(mockWs.close).toHaveBeenCalled();
      expect(connectionSpy).toHaveBeenCalledWith('disconnected');
    });
  });

  describe('Configuration and Edge Cases', () => {
    it('should respect reconnection limits', async () => {
      const limitedClient = new WebSocketClient({
        url: 'ws://localhost:8082',
        reconnectInterval: 100,
        maxReconnectAttempts: 2,
      });

      const connectionSpy = vi.fn();
      limitedClient.on('connectionChange', connectionSpy);

      await limitedClient.connect();

      // Simulate multiple connection failures
      for (let i = 0; i < 3; i++) {
        const errorHandler = mockWs.on.mock.calls.find(call => call[0] === 'error')?.[1];
        if (errorHandler) {
          errorHandler(new Error('Connection failed'));
        }

        const closeHandler = mockWs.on.mock.calls.find(call => call[0] === 'close')?.[1];
        if (closeHandler) {
          closeHandler(1006, 'Connection lost');
        }
      }

      // Should eventually give up and emit error status
      expect(connectionSpy).toHaveBeenCalledWith('error');
      
      limitedClient.disconnect();
    });

    it('should handle concurrent connection attempts', async () => {
      // Multiple connect calls should not create multiple connections
      const connectPromises = [
        client.connect(),
        client.connect(),
        client.connect(),
      ];

      await Promise.all(connectPromises);

      // Should only create one WebSocket instance
      expect(MockWebSocket).toHaveBeenCalledTimes(1);
    });

    it('should generate unique IDs for agent requests', async () => {
      const agentRequestSpy = vi.fn();
      client.on('agentRequest', agentRequestSpy);

      await client.connect();

      const messageHandler = mockWs.on.mock.calls.find(call => call[0] === 'message')?.[1];
      
      // Send multiple agent requests
      const baseRequest = {
        sessionId: 'test-session',
        summary: 'Test request',
        context: 'Test context',
        timestamp: new Date().toISOString(),
        timeout: 30000,
      };

      for (let i = 0; i < 3; i++) {
        const message = JSON.stringify({
          type: 'agent_request',
          data: baseRequest,
        });

        if (messageHandler) {
          messageHandler(message);
        }
      }

      expect(agentRequestSpy).toHaveBeenCalledTimes(3);
      
      // Check that all IDs are unique
      const calls = agentRequestSpy.mock.calls;
      const ids = calls.map(call => call[0].id);
      const uniqueIds = new Set(ids);
      
      expect(uniqueIds.size).toBe(3); // All IDs should be unique
    });
  });
});
