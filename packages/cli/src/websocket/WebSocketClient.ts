import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { AgentRequest as BaseAgentRequest, HumanResponse, withErrorHandling } from '@powersteer/common';
import { v4 as uuidv4 } from 'uuid';
import { AgentRequest } from '../components/PowersteerApp.js';

/**
 * WebSocket client configuration
 */
export interface WebSocketClientConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  sessionId?: string;
}

/**
 * Connection status type
 */
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

/**
 * WebSocket client events
 */
interface WebSocketClientEvents {
  connectionChange: (status: ConnectionStatus) => void;
  agentRequest: (request: AgentRequest) => void;
  error: (error: Error) => void;
}

/**
 * WebSocket client for CLI communication with server
 */
export class WebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: WebSocketClientConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private shouldReconnect = true;
  private pingTimer: NodeJS.Timeout | null = null;
  private lastPongReceived = Date.now();
  private readonly PING_INTERVAL = 30000; // 30 seconds
  private readonly PONG_TIMEOUT = 10000; // 10 seconds
  private currentSessionId: string | null = null;

  constructor(config: WebSocketClientConfig) {
    super();
    this.config = config;
    this.currentSessionId = config.sessionId || null;
  }

  /**
   * Connect to WebSocket server
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.shouldReconnect = true;
    this.emit('connectionChange', 'connecting');

    try {
      this.ws = new WebSocket(this.config.url);
      this.setupEventHandlers();
    } catch (error) {
      this.isConnecting = false;
      this.emit('connectionChange', 'error');
      this.emit('error', error as Error);
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  public disconnect(): void {
    this.shouldReconnect = false;
    this.clearReconnectTimer();
    this.clearPingTimer();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.emit('connectionChange', 'disconnected');
  }

  /**
   * Send human response to server
   */
  public async sendHumanResponse(response: HumanResponse): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected');
    }

    if (!this.currentSessionId) {
      throw new Error('No active session - cannot send response');
    }

    const message = {
      type: 'human_response',
      sessionId: this.currentSessionId,
      payload: response,
      timestamp: new Date().toISOString(),
    };

    return new Promise((resolve, reject) => {
      this.ws!.send(JSON.stringify(message), (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.lastPongReceived = Date.now();
      this.startPingTimer();

      // Send connect message to join the most recent session
      this.sendConnectMessage();

      this.emit('connectionChange', 'connected');
    });

    this.ws.on('message', (data) => {
      this.handleMessage(data).catch(error => {
        this.emit('error', error);
      });
    });

    this.ws.on('close', (code, reason) => {
      this.isConnecting = false;
      this.ws = null;
      this.clearPingTimer();

      console.log(`WebSocket connection closed: code=${code}, reason=${reason || 'No reason provided'}`);

      if (this.shouldReconnect) {
        this.emit('connectionChange', 'disconnected');
        this.scheduleReconnect();
      }
    });

    this.ws.on('error', (error) => {
      this.isConnecting = false;
      this.emit('connectionChange', 'error');
      this.emit('error', error);
      
      if (this.shouldReconnect) {
        this.scheduleReconnect();
      }
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage = withErrorHandling(async (data: WebSocket.Data) => {
    try {
      const message = JSON.parse(data.toString());

      switch (message.type) {
        case 'agent_request':
          // Store session ID for future responses
          this.currentSessionId = message.sessionId;

          // Add ID to the base agent request for CLI usage
          const baseRequest = message.payload as BaseAgentRequest;
          const agentRequest: AgentRequest = {
            ...baseRequest,
            id: uuidv4(),
          };
          this.emit('agentRequest', agentRequest);
          break;
        case 'session_update':
          // Handle session updates (connection confirmations, etc.)
          console.log(`Session update: ${message.message} (Session: ${message.sessionId})`);
          this.currentSessionId = message.sessionId;
          break;
        case 'error':
          // Handle error messages from server
          const errorMsg = message.error?.message || message.message || 'Unknown error';
          console.error('Server error:', errorMsg);
          if (message.error?.details) {
            console.error('Error details:', message.error.details);
          }
          break;
        case 'pong':
          // Update last pong received time for connection health monitoring
          this.lastPongReceived = Date.now();
          break;
        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      this.emit('error', new Error(`Failed to parse message: ${error}`));
    }
  });

  /**
   * Schedule reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (!this.shouldReconnect || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.emit('connectionChange', 'error');
      console.error(`Max reconnection attempts (${this.config.maxReconnectAttempts}) reached`);
      return;
    }

    this.clearReconnectTimer();
    this.reconnectAttempts++;

    // Exponential backoff with jitter
    const baseDelay = this.config.reconnectInterval;
    const exponentialDelay = baseDelay * Math.pow(2, this.reconnectAttempts - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    const delay = Math.min(exponentialDelay + jitter, 30000); // Cap at 30 seconds

    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${Math.round(delay)}ms`);

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Clear reconnection timer
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Start ping timer for connection health monitoring
   */
  private startPingTimer(): void {
    this.clearPingTimer();

    this.pingTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // Check if we received a pong recently
        const timeSinceLastPong = Date.now() - this.lastPongReceived;

        if (timeSinceLastPong > this.PING_INTERVAL + this.PONG_TIMEOUT) {
          console.warn('Connection appears to be dead, forcing reconnection');
          this.ws.close();
          return;
        }

        // Send ping
        this.ws.send(JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() }));
      }
    }, this.PING_INTERVAL);
  }

  /**
   * Send connect message to join a session
   */
  private sendConnectMessage(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    const connectMessage = {
      type: 'connect',
      clientType: 'cli',
      sessionId: this.currentSessionId,
      timestamp: new Date().toISOString(),
    };

    console.log('Sending connect message:', JSON.stringify(connectMessage));
    this.ws.send(JSON.stringify(connectMessage));
  }

  /**
   * Clear ping timer
   */
  private clearPingTimer(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  /**
   * Type-safe event emitter methods
   */
  public on<K extends keyof WebSocketClientEvents>(
    event: K,
    listener: WebSocketClientEvents[K]
  ): this {
    return super.on(event, listener);
  }

  public emit<K extends keyof WebSocketClientEvents>(
    event: K,
    ...args: Parameters<WebSocketClientEvents[K]>
  ): boolean {
    return super.emit(event, ...args);
  }
}
