import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import WebSocket from 'ws';
import { WebSocketClient } from './WebSocketClient.js';
import { HumanResponse } from '@powersteer/common';
import { AgentRequest } from '../components/PowersteerApp.js';

// Mock WebSocket
vi.mock('ws');
const MockWebSocket = vi.mocked(WebSocket);

describe('WebSocketClient', () => {
  let client: WebSocketClient;
  let mockWs: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create mock WebSocket instance
    mockWs = {
      readyState: WebSocket.OPEN,
      send: vi.fn(),
      close: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    };

    // Mock WebSocket constructor
    MockWebSocket.mockImplementation(() => mockWs);

    // Create client
    client = new WebSocketClient({
      url: 'ws://localhost:8082',
      reconnectInterval: 1000,
      maxReconnectAttempts: 3,
    });
  });

  afterEach(() => {
    client.disconnect();
  });

  describe('connection management', () => {
    it('should emit connecting status when connecting', async () => {
      const statusSpy = vi.fn();
      client.on('connectionChange', statusSpy);

      await client.connect();

      expect(statusSpy).toHaveBeenCalledWith('connecting');
      expect(MockWebSocket).toHaveBeenCalledWith('ws://localhost:8082');
    });

    it('should emit connected status when WebSocket opens', async () => {
      const statusSpy = vi.fn();
      client.on('connectionChange', statusSpy);

      await client.connect();

      // Simulate WebSocket open event
      const openHandler = mockWs.on.mock.calls.find(([event]) => event === 'open')?.[1];
      openHandler?.();

      expect(statusSpy).toHaveBeenCalledWith('connected');
    });

    it('should emit disconnected status when WebSocket closes', async () => {
      const statusSpy = vi.fn();
      client.on('connectionChange', statusSpy);

      await client.connect();

      // Simulate WebSocket close event
      const closeHandler = mockWs.on.mock.calls.find(([event]) => event === 'close')?.[1];
      closeHandler?.(1000, 'Normal closure');

      expect(statusSpy).toHaveBeenCalledWith('disconnected');
    });

    it('should emit error status when WebSocket errors', async () => {
      const statusSpy = vi.fn();
      const errorSpy = vi.fn();
      client.on('connectionChange', statusSpy);
      client.on('error', errorSpy);

      await client.connect();

      // Simulate WebSocket error event
      const error = new Error('Connection failed');
      const errorHandler = mockWs.on.mock.calls.find(([event]) => event === 'error')?.[1];
      errorHandler?.(error);

      expect(statusSpy).toHaveBeenCalledWith('error');
      expect(errorSpy).toHaveBeenCalledWith(error);
    });
  });

  describe('message handling', () => {
    it('should handle agent request messages', async () => {
      const requestSpy = vi.fn();
      client.on('agentRequest', requestSpy);

      await client.connect();

      const agentRequest: AgentRequest = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        sessionId: '123e4567-e89b-12d3-a456-************',
        type: 'information',
        priority: 'normal',
        summary: 'Test agent request',
        timeout: 30000,
        timestamp: '2024-01-01T12:00:00Z',
      };

      const message = {
        type: 'agent_request',
        data: agentRequest,
      };

      // Simulate incoming message
      const messageHandler = mockWs.on.mock.calls.find(([event]) => event === 'message')?.[1];
      messageHandler?.(JSON.stringify(message));

      // Check that the spy was called with an object that has the expected structure
      expect(requestSpy).toHaveBeenCalledTimes(1);
      const calledWith = requestSpy.mock.calls[0][0];
      expect(calledWith).toMatchObject({
        sessionId: agentRequest.sessionId,
        type: agentRequest.type,
        priority: agentRequest.priority,
        summary: agentRequest.summary,
        timeout: agentRequest.timeout,
        timestamp: agentRequest.timestamp,
      });
      expect(calledWith.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/); // UUID format
    });

    it('should handle malformed messages gracefully', async () => {
      const errorSpy = vi.fn();
      client.on('error', errorSpy);

      await client.connect();

      // Simulate malformed message
      const messageHandler = mockWs.on.mock.calls.find(([event]) => event === 'message')?.[1];
      messageHandler?.('invalid json');

      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('sending responses', () => {
    it('should send human response when connected', async () => {
      await client.connect();

      // Simulate connected state
      mockWs.readyState = WebSocket.OPEN;
      mockWs.send.mockImplementation((data, callback) => callback?.());

      const response: HumanResponse = {
        sessionId: '123e4567-e89b-12d3-a456-************',
        response: 'Test human response',
        timestamp: '2024-01-01T12:00:00Z',
      };

      await client.sendHumanResponse(response);

      expect(mockWs.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'human_response',
          data: response,
        }),
        expect.any(Function)
      );
    });

    it('should throw error when not connected', async () => {
      mockWs.readyState = WebSocket.CLOSED;

      const response: HumanResponse = {
        sessionId: '123e4567-e89b-12d3-a456-************',
        response: 'Test human response',
        timestamp: '2024-01-01T12:00:00Z',
      };

      await expect(client.sendHumanResponse(response)).rejects.toThrow('WebSocket not connected');
    });
  });

  describe('disconnection', () => {
    it('should close WebSocket and emit disconnected status', async () => {
      const statusSpy = vi.fn();
      client.on('connectionChange', statusSpy);

      await client.connect();
      client.disconnect();

      expect(mockWs.close).toHaveBeenCalled();
      expect(statusSpy).toHaveBeenCalledWith('disconnected');
    });
  });
});
