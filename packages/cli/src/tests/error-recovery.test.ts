import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { WebSocketClient } from '../websocket/WebSocketClient.js';
import { ErrorRecovery } from '../components/ErrorBoundary.js';
import WebSocket from 'ws';

// Mock WebSocket
vi.mock('ws', () => ({
  default: vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    send: vi.fn(),
    close: vi.fn(),
    readyState: 1, // OPEN
  })),
  WebSocket: {
    OPEN: 1,
    CLOSED: 3,
  },
}));

describe('Error Recovery & Edge Cases', () => {
  let mockWebSocket: any;
  let client: WebSocketClient;

  beforeEach(() => {
    vi.clearAllMocks();
    mockWebSocket = {
      on: vi.fn(),
      send: vi.fn(),
      close: vi.fn(),
      readyState: WebSocket.OPEN,
    };
    (WebSocket as any).mockImplementation(() => mockWebSocket);
  });

  afterEach(() => {
    if (client) {
      client.disconnect();
    }
  });

  describe('WebSocket Connection Recovery', () => {
    it('should handle connection failures with exponential backoff', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 100,
        maxReconnectAttempts: 3,
      };

      client = new WebSocketClient(config);
      
      // Mock connection failure
      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(new Error('Connection failed')), 10);
        }
      });

      const connectionChanges: string[] = [];
      client.on('connectionChange', (status) => {
        connectionChanges.push(status);
      });

      await client.connect();
      
      // Wait for reconnection attempts
      await new Promise(resolve => setTimeout(resolve, 500));

      expect(connectionChanges).toContain('connecting');
      expect(connectionChanges).toContain('error');
    });

    it('should stop reconnecting after max attempts', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 50,
        maxReconnectAttempts: 2,
      };

      client = new WebSocketClient(config);
      
      // Mock persistent connection failure
      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(new Error('Persistent failure')), 10);
        }
      });

      const connectionChanges: string[] = [];
      client.on('connectionChange', (status) => {
        connectionChanges.push(status);
      });

      await client.connect();
      
      // Wait for all reconnection attempts
      await new Promise(resolve => setTimeout(resolve, 300));

      // Should eventually give up and emit error status
      expect(connectionChanges.filter(s => s === 'error').length).toBeGreaterThan(0);
    });

    it('should handle ping/pong for connection health monitoring', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 1000,
        maxReconnectAttempts: 3,
      };

      client = new WebSocketClient(config);
      
      let pingCallback: Function;
      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'open') {
          setTimeout(() => callback(), 10);
        } else if (event === 'message') {
          pingCallback = callback;
        }
      });

      await client.connect();
      
      // Wait for connection to establish
      await new Promise(resolve => setTimeout(resolve, 50));

      // Simulate pong response
      if (pingCallback) {
        pingCallback(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
      }

      expect(mockWebSocket.on).toHaveBeenCalledWith('open', expect.any(Function));
      expect(mockWebSocket.on).toHaveBeenCalledWith('message', expect.any(Function));
    });
  });

  describe('Session Timeout Recovery', () => {
    it('should handle session timeout gracefully', () => {
      const mockRecover = vi.fn();
      const sessionId = 'test-session-123';

      ErrorRecovery.handleSessionTimeout(sessionId, mockRecover);

      expect(mockRecover).toHaveBeenCalled();
    });
  });

  describe('Offline Mode and Graceful Degradation', () => {
    it('should enable offline mode when server is unavailable', () => {
      const offlineMode = ErrorRecovery.enableOfflineMode();

      expect(offlineMode.isOffline).toBe(true);
      expect(offlineMode.queuedMessages).toEqual([]);

      // Test message queuing
      const testMessage = { type: 'test', content: 'Hello' };
      offlineMode.addMessage(testMessage);

      expect(offlineMode.queuedMessages).toHaveLength(1);
      expect(offlineMode.queuedMessages[0]).toMatchObject({
        ...testMessage,
        queued: true,
      });

      // Test message flushing
      const flushedMessages = offlineMode.flushMessages();
      expect(flushedMessages).toHaveLength(1);
      expect(offlineMode.queuedMessages).toHaveLength(0);
    });
  });

  describe('Network Failure Handling', () => {
    it('should detect network failures and attempt recovery', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 100,
        maxReconnectAttempts: 3,
      };

      client = new WebSocketClient(config);
      
      // Mock network failure scenario
      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(1006, 'Network error'), 10); // 1006 = abnormal closure
        }
      });

      const connectionChanges: string[] = [];
      client.on('connectionChange', (status) => {
        connectionChanges.push(status);
      });

      await client.connect();
      
      // Simulate network failure
      mockWebSocket.on.mock.calls
        .find(([event]) => event === 'close')?.[1](1006, 'Network error');

      await new Promise(resolve => setTimeout(resolve, 150));

      expect(connectionChanges).toContain('disconnected');
    });
  });

  describe('Edge Cases', () => {
    it('should handle malformed messages gracefully', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 1000,
        maxReconnectAttempts: 3,
      };

      client = new WebSocketClient(config);
      
      let messageCallback: Function;
      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'message') {
          messageCallback = callback;
        }
      });

      const errors: Error[] = [];
      client.on('error', (error) => {
        errors.push(error);
      });

      await client.connect();

      // Send malformed JSON
      if (messageCallback) {
        messageCallback('invalid json {');
      }

      expect(errors).toHaveLength(1);
      expect(errors[0].message).toContain('Failed to parse message');
    });

    it('should handle rapid connect/disconnect cycles', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 50,
        maxReconnectAttempts: 5,
      };

      client = new WebSocketClient(config);

      // Rapid connect/disconnect
      for (let i = 0; i < 3; i++) {
        await client.connect();
        client.disconnect();
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Should handle gracefully without errors
      expect(true).toBe(true); // Test passes if no exceptions thrown
    });

    it('should handle WebSocket state transitions correctly', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 100,
        maxReconnectAttempts: 3,
      };

      client = new WebSocketClient(config);

      // Test connecting when already connected
      mockWebSocket.readyState = WebSocket.OPEN;
      await client.connect();
      await client.connect(); // Second call should be ignored

      expect(WebSocket).toHaveBeenCalledTimes(1);
    });
  });

  describe('System Robustness', () => {
    it('should maintain system stability under stress', async () => {
      const config = {
        url: 'ws://localhost:8080',
        reconnectInterval: 10,
        maxReconnectAttempts: 10,
      };

      client = new WebSocketClient(config);

      // Simulate high-frequency events
      const events = ['open', 'close', 'error', 'message'];
      const callbacks: { [key: string]: Function } = {};

      mockWebSocket.on.mockImplementation((event: string, callback: Function) => {
        callbacks[event] = callback;
      });

      await client.connect();

      // Trigger rapid events
      for (let i = 0; i < 50; i++) {
        const event = events[i % events.length];
        if (callbacks[event]) {
          try {
            if (event === 'message') {
              callbacks[event](JSON.stringify({ type: 'test', data: i }));
            } else if (event === 'close') {
              callbacks[event](1000, 'Normal closure');
            } else if (event === 'error') {
              callbacks[event](new Error(`Test error ${i}`));
            } else {
              callbacks[event]();
            }
          } catch (error) {
            // Should handle errors gracefully
          }
        }
        await new Promise(resolve => setTimeout(resolve, 1));
      }

      // System should remain stable
      expect(true).toBe(true);
    });
  });
});
