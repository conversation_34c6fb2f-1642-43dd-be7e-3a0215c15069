import React from 'react';
import { describe, it, expect } from 'vitest';
import { render } from 'ink-testing-library';
import { AgentSummary } from './AgentSummary.js';
import { AgentRequest } from './PowersteerApp.js';

describe('AgentSummary', () => {
  it('should display empty state when no request', () => {
    const { lastFrame } = render(<AgentSummary request={null} />);
    
    expect(lastFrame()).toContain('No active request');
  });

  it('should display agent request details', () => {
    const request: AgentRequest = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      sessionId: '123e4567-e89b-12d3-a456-426614174001',
      type: 'information',
      priority: 'normal',
      summary: 'This is a test agent request summary that explains what the agent needs.',
      timeout: 30000,
      timestamp: '2024-01-01T12:00:00Z',
    };

    const { lastFrame } = render(<AgentSummary request={request} />);
    
    const output = lastFrame();
    expect(output).toContain('Request ID:');
    expect(output).toContain('123e4567');
    expect(output).toContain('Session:');
    expect(output).toContain('123e4567');
    expect(output).toContain('Time:');
    expect(output).toContain('Agent Summary:');
    expect(output).toContain('This is a test agent request summary');
  });

  it('should display timeout information when present', () => {
    const request: AgentRequest = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      sessionId: '123e4567-e89b-12d3-a456-426614174001',
      type: 'information',
      priority: 'normal',
      summary: 'Test request with timeout',
      timeout: 60000,
      timestamp: '2024-01-01T12:00:00Z',
    };

    const { lastFrame } = render(<AgentSummary request={request} />);
    
    expect(lastFrame()).toContain('Timeout:');
    expect(lastFrame()).toContain('60000ms');
  });

  it('should not display timeout when not present', () => {
    const request: AgentRequest = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      sessionId: '123e4567-e89b-12d3-a456-426614174001',
      type: 'information',
      priority: 'normal',
      summary: 'Test request without timeout',
      timeout: 30000,
      timestamp: '2024-01-01T12:00:00Z',
    };

    const { lastFrame } = render(<AgentSummary request={request} />);
    
    expect(lastFrame()).not.toContain('Timeout:');
  });

  it('should display instructions for user', () => {
    const request: AgentRequest = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      sessionId: '123e4567-e89b-12d3-a456-426614174001',
      type: 'information',
      priority: 'normal',
      summary: 'Test request',
      timeout: 30000,
      timestamp: '2024-01-01T12:00:00Z',
    };

    const { lastFrame } = render(<AgentSummary request={request} />);
    
    expect(lastFrame()).toContain('💡');
    expect(lastFrame()).toContain('Review the agent\'s summary');
    expect(lastFrame()).toContain('provide your response');
  });
});
