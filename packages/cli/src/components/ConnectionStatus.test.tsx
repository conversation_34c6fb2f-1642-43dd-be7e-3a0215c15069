import React from 'react';
import { describe, it, expect } from 'vitest';
import { render } from 'ink-testing-library';
import { ConnectionStatus } from './ConnectionStatus.js';

describe('ConnectionStatus', () => {
  const serverUrl = 'ws://localhost:8082';

  it('should display connecting status', () => {
    const { lastFrame } = render(
      <ConnectionStatus status="connecting" serverUrl={serverUrl} />
    );
    
    const output = lastFrame();
    expect(output).toContain('🔄');
    expect(output).toContain('Connecting...');
    expect(output).toContain(serverUrl);
    expect(output).toContain('Attempting to connect');
  });

  it('should display connected status', () => {
    const { lastFrame } = render(
      <ConnectionStatus status="connected" serverUrl={serverUrl} />
    );
    
    const output = lastFrame();
    expect(output).toContain('✅');
    expect(output).toContain('Connected');
    expect(output).toContain(serverUrl);
    expect(output).not.toContain('Attempting to connect');
  });

  it('should display disconnected status', () => {
    const { lastFrame } = render(
      <ConnectionStatus status="disconnected" serverUrl={serverUrl} />
    );
    
    const output = lastFrame();
    expect(output).toContain('❌');
    expect(output).toContain('Disconnected');
    expect(output).toContain(serverUrl);
    expect(output).toContain('Will attempt to reconnect');
  });

  it('should display error status', () => {
    const { lastFrame } = render(
      <ConnectionStatus status="error" serverUrl={serverUrl} />
    );
    
    const output = lastFrame();
    expect(output).toContain('⚠️');
    expect(output).toContain('Connection Error');
    expect(output).toContain(serverUrl);
    expect(output).toContain('Check server and try again');
  });

  it('should display server URL for all statuses', () => {
    const statuses = ['connecting', 'connected', 'disconnected', 'error'] as const;
    
    statuses.forEach(status => {
      const { lastFrame } = render(
        <ConnectionStatus status={status} serverUrl={serverUrl} />
      );
      
      expect(lastFrame()).toContain(serverUrl);
    });
  });
});
