import React from 'react';
import { Box, Text } from 'ink';

/**
 * Connection status type
 */
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

/**
 * ConnectionStatus component props
 */
interface ConnectionStatusProps {
  status: ConnectionStatus;
  serverUrl: string;
}

/**
 * Get status display information
 */
function getStatusInfo(status: ConnectionStatus) {
  switch (status) {
    case 'connecting':
      return {
        icon: '🔄',
        text: 'Connecting...',
        color: 'yellow' as const,
      };
    case 'connected':
      return {
        icon: '✅',
        text: 'Connected',
        color: 'green' as const,
      };
    case 'disconnected':
      return {
        icon: '❌',
        text: 'Disconnected',
        color: 'red' as const,
      };
    case 'error':
      return {
        icon: '⚠️',
        text: 'Connection Error',
        color: 'red' as const,
      };
    default:
      return {
        icon: '❓',
        text: 'Unknown',
        color: 'gray' as const,
      };
  }
}

/**
 * ConnectionStatus component - displays WebSocket connection status
 */
export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status, serverUrl }) => {
  const statusInfo = getStatusInfo(status);

  return (
    <Box borderStyle="single" paddingX={1} marginBottom={1}>
      <Box flexGrow={1}>
        <Text>
          {statusInfo.icon}{' '}
          <Text color={statusInfo.color} bold>
            {statusInfo.text}
          </Text>
          {' - '}
          <Text dimColor>{serverUrl}</Text>
        </Text>
      </Box>
      
      {/* Additional status information */}
      {status === 'connecting' && (
        <Text dimColor> (Attempting to connect...)</Text>
      )}
      
      {status === 'disconnected' && (
        <Text dimColor> (Will attempt to reconnect)</Text>
      )}
      
      {status === 'error' && (
        <Text dimColor> (Check server and try again)</Text>
      )}
    </Box>
  );
};
