import React, { Component, ReactNode } from 'react';
import { Box, Text } from 'ink';

/**
 * Error boundary state
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

/**
 * Error boundary props
 */
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * React Error Boundary for CLI application
 * Catches and handles React component errors gracefully
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error for debugging
    console.error('React Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI or default error display
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box flexDirection="column" padding={2}>
          <Box borderStyle="double" borderColor="red" padding={1} marginBottom={1}>
            <Text color="red" bold>
              ⚠️ Application Error
            </Text>
          </Box>
          
          <Box flexDirection="column" marginBottom={2}>
            <Text color="red">
              An unexpected error occurred in the CLI application:
            </Text>
            <Text color="gray" wrap="wrap">
              {this.state.error?.message || 'Unknown error'}
            </Text>
          </Box>

          <Box flexDirection="column" marginBottom={2}>
            <Text bold>Troubleshooting Steps:</Text>
            <Text>• Check if the Powersteer server is running</Text>
            <Text>• Verify the server URL is correct</Text>
            <Text>• Try restarting the CLI application</Text>
            <Text>• Check network connectivity</Text>
          </Box>

          <Box flexDirection="column">
            <Text dimColor>
              Press Ctrl+C to exit and restart the application
            </Text>
            {process.env.NODE_ENV === 'development' && (
              <Box flexDirection="column" marginTop={1}>
                <Text color="yellow">Development Info:</Text>
                <Text color="gray" wrap="wrap">
                  {this.state.error?.stack}
                </Text>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback} onError={onError}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

/**
 * Error recovery utilities
 */
export class ErrorRecovery {
  /**
   * Attempt to recover from WebSocket connection errors
   */
  static async recoverWebSocketConnection(
    client: any,
    maxAttempts: number = 5,
    baseDelay: number = 1000
  ): Promise<boolean> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        await client.connect();
        return true;
      } catch (error) {
        console.error(`Connection attempt ${attempt} failed:`, error);
        
        if (attempt < maxAttempts) {
          // Exponential backoff with jitter
          const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    return false;
  }

  /**
   * Handle session timeout recovery
   */
  static handleSessionTimeout(sessionId: string, onRecover?: () => void): void {
    console.warn(`Session ${sessionId} timed out, attempting recovery...`);
    
    // Clear any pending operations
    // Reset application state
    // Attempt to reconnect
    
    if (onRecover) {
      onRecover();
    }
  }

  /**
   * Graceful degradation when server is unavailable
   */
  static enableOfflineMode(): {
    isOffline: boolean;
    queuedMessages: any[];
    addMessage: (message: any) => void;
    flushMessages: () => any[];
  } {
    const queuedMessages: any[] = [];
    
    return {
      isOffline: true,
      queuedMessages,
      addMessage: (message: any) => {
        queuedMessages.push({
          ...message,
          timestamp: new Date().toISOString(),
          queued: true,
        });
      },
      flushMessages: () => {
        const messages = [...queuedMessages];
        queuedMessages.length = 0;
        return messages;
      },
    };
  }
}
