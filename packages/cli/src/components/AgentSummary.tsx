import React from 'react';
import { Box, Text } from 'ink';
import { AgentRequest } from './PowersteerApp.js';

/**
 * AgentSummary component props
 */
interface AgentSummaryProps {
  request: AgentRequest | null;
}

/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp: string): string {
  return new Date(timestamp).toLocaleString('en-US', {
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
}

/**
 * AgentSummary component - displays current agent request details
 */
export const AgentSummary: React.FC<AgentSummaryProps> = ({ request }) => {
  if (!request) {
    return (
      <Box flexDirection="column" paddingX={1} flexGrow={1}>
        <Box justifyContent="center" alignItems="center" flexGrow={1}>
          <Text dimColor>No active request</Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" paddingX={1} flexGrow={1}>
      {/* Request Header */}
      <Box flexDirection="column" marginBottom={1}>
        <Box>
          <Text bold color="blue">Request ID:</Text>
          <Text> {request.id.slice(0, 8)}...</Text>
        </Box>
        <Box>
          <Text bold color="blue">Session:</Text>
          <Text> {request.sessionId.slice(0, 8)}...</Text>
        </Box>
        <Box>
          <Text bold color="blue">Time:</Text>
          <Text> {formatTimestamp(request.timestamp)}</Text>
        </Box>
      </Box>

      {/* Summary Content */}
      <Box flexDirection="column" flexGrow={1}>
        <Box marginBottom={1}>
          <Text bold color="yellow">
            Agent Summary:
          </Text>
        </Box>
        <Box borderStyle="single" paddingX={1} paddingY={1} flexGrow={1}>
          <Text wrap="wrap">{request.summary}</Text>
        </Box>
      </Box>

      {/* Timeout Information - only show if different from default */}
      {request.timeout && request.timeout !== 30000 && (
        <Box marginTop={1}>
          <Text bold color="red">Timeout:</Text>
          <Text> {request.timeout}ms</Text>
        </Box>
      )}

      {/* Instructions */}
      <Box marginTop={1} borderStyle="single" paddingX={1}>
        <Text dimColor wrap="wrap">
          💡 Review the agent's summary above and provide your response in the input field below.
        </Text>
      </Box>
    </Box>
  );
};
