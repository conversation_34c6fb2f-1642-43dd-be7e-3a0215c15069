import React from 'react';
import { describe, it, expect } from 'vitest';
import { render } from 'ink-testing-library';
import { MessageView } from './MessageView.js';
import { Message } from '@powersteer/common';

describe('MessageView', () => {
  it('should display empty state when no messages', () => {
    const { lastFrame } = render(<MessageView messages={[]} />);
    
    expect(lastFrame()).toContain('No messages yet');
    expect(lastFrame()).toContain('Waiting for agent requests');
  });

  it('should display agent request messages', () => {
    const messages: Message[] = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'agent_request',
        content: 'Test agent request message',
        timestamp: new Date('2024-01-01T12:00:00Z'),
        sessionId: '123e4567-e89b-12d3-a456-426614174001',
      },
    ];

    const { lastFrame } = render(<MessageView messages={messages} />);
    
    expect(lastFrame()).toContain('[AGENT]');
    expect(lastFrame()).toContain('Test agent request message');
    expect(lastFrame()).toMatch(/\d{2}:\d{2}:\d{2}/); // Match any time format
    expect(lastFrame()).toContain('Session: 123e4567');
  });

  it('should display human response messages', () => {
    const messages: Message[] = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'human_response',
        content: 'Test human response message',
        timestamp: new Date('2024-01-01T12:00:00Z'),
        sessionId: '123e4567-e89b-12d3-a456-426614174001',
      },
    ];

    const { lastFrame } = render(<MessageView messages={messages} />);
    
    expect(lastFrame()).toContain('[HUMAN]');
    expect(lastFrame()).toContain('Test human response message');
    expect(lastFrame()).toMatch(/\d{2}:\d{2}:\d{2}/); // Match any time format
  });

  it('should display multiple messages in order', () => {
    const messages: Message[] = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'agent_request',
        content: 'First message',
        timestamp: new Date('2024-01-01T12:00:00Z'),
        sessionId: '123e4567-e89b-12d3-a456-426614174001',
      },
      {
        id: '123e4567-e89b-12d3-a456-426614174002',
        type: 'human_response',
        content: 'Second message',
        timestamp: new Date('2024-01-01T12:01:00Z'),
        sessionId: '123e4567-e89b-12d3-a456-426614174001',
      },
    ];

    const { lastFrame } = render(<MessageView messages={messages} />);
    
    const output = lastFrame();
    expect(output).toContain('[AGENT]');
    expect(output).toContain('First message');
    expect(output).toContain('[HUMAN]');
    expect(output).toContain('Second message');
    
    // Check order - agent message should appear before human message
    const agentIndex = output?.indexOf('[AGENT]') ?? -1;
    const humanIndex = output?.indexOf('[HUMAN]') ?? -1;
    expect(agentIndex).toBeLessThan(humanIndex);
  });

  it('should handle messages without session ID', () => {
    const messages: Message[] = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'system',
        content: 'System message',
        timestamp: new Date('2024-01-01T12:00:00Z'),
      },
    ];

    const { lastFrame } = render(<MessageView messages={messages} />);
    
    expect(lastFrame()).toContain('[SYSTEM]');
    expect(lastFrame()).toContain('System message');
    expect(lastFrame()).not.toContain('Session:');
  });
});
