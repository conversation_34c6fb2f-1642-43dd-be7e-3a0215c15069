import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from 'ink-testing-library';
import { InputBox } from './InputBox.js';

// Mock the useInput hook to avoid stdin.ref issues in tests
vi.mock('ink', async () => {
  const actual = await vi.importActual('ink');
  return {
    ...actual,
    useInput: vi.fn(),
  };
});

describe('InputBox', () => {
  let mockOnChange: ReturnType<typeof vi.fn>;
  let mockOnSubmit: ReturnType<typeof vi.fn>;
  let mockOnCommand: (command: string) => boolean;

  beforeEach(() => {
    mockOnChange = vi.fn();
    mockOnSubmit = vi.fn();
    mockOnCommand = vi.fn().mockReturnValue(true) as (command: string) => boolean;
  });

  it('should display placeholder when empty', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        placeholder="Enter your response..."
      />
    );

    expect(lastFrame()).toContain('Enter your response...');
    expect(lastFrame()).toContain('> ');
  });

  it('should show disabled state', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        disabled={true}
        placeholder="Disabled input"
      />
    );

    expect(lastFrame()).toContain('Disabled input');
    expect(lastFrame()).toContain('Input disabled - waiting for connection or request');
  });

  it('should display help text when enabled', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        disabled={false}
      />
    );

    expect(lastFrame()).toContain('Press Enter to send, /exit to quit, Ctrl+U to clear');
  });

  it('should display disabled help text when disabled', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        disabled={true}
      />
    );

    expect(lastFrame()).toContain('Input disabled - waiting for connection or request');
  });

  it('should render with cursor when value is present', () => {
    const { lastFrame } = render(
      <InputBox
        value="test"
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
      />
    );

    const output = lastFrame();
    // Strip ANSI escape codes for easier testing
    const cleanOutput = output?.replace(/\u001b\[[0-9;]*m/g, '') || '';
    expect(cleanOutput).toContain('test');
    expect(cleanOutput).toContain('> ');
  });

  it('should show cursor at beginning when empty', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
      />
    );

    // Should show cursor and placeholder
    expect(lastFrame()).toContain('> ');
    expect(lastFrame()).toContain('Type your response...');
  });

  it('should use custom placeholder', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        placeholder="Custom placeholder text"
      />
    );

    expect(lastFrame()).toContain('Custom placeholder text');
  });

  it('should show green prompt when enabled', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        disabled={false}
      />
    );

    // The prompt should be colored (we can't easily test colors, but we can test structure)
    expect(lastFrame()).toContain('> ');
  });

  it('should show gray prompt when disabled', () => {
    const { lastFrame } = render(
      <InputBox
        value=""
        onChange={mockOnChange}
        onSubmit={mockOnSubmit}
        onCommand={mockOnCommand}
        disabled={true}
      />
    );

    // The prompt should be colored differently when disabled
    expect(lastFrame()).toContain('> ');
  });
});
