import React, { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';

/**
 * Session information for selection
 */
interface SessionInfo {
  sessionId: string;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
}

/**
 * Props for SessionSelector component
 */
interface SessionSelectorProps {
  sessions: SessionInfo[];
  onSelect: (sessionId: string) => void;
  onCancel: () => void;
}

/**
 * Component for selecting a session to resume
 */
export const SessionSelector: React.FC<SessionSelectorProps> = ({
  sessions,
  onSelect,
  onCancel,
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  /**
   * Handle keyboard input for navigation
   */
  useInput((input, key) => {
    if (key.upArrow && selectedIndex > 0) {
      setSelectedIndex(selectedIndex - 1);
    } else if (key.downArrow && selectedIndex < sessions.length - 1) {
      setSelectedIndex(selectedIndex + 1);
    } else if (key.return) {
      if (sessions[selectedIndex]) {
        onSelect(sessions[selectedIndex].sessionId);
      }
    } else if (key.escape || input === 'q') {
      onCancel();
    }
  });

  /**
   * Format date for display
   */
  const formatDate = useCallback((date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Less than an hour ago';
    }
  }, []);

  if (sessions.length === 0) {
    return (
      <Box flexDirection="column" padding={1}>
        <Text bold color="yellow">📂 No Previous Sessions Found</Text>
        <Text>No conversation history files found in /tmp/</Text>
        <Text dimColor>Press any key to continue...</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" padding={1}>
      <Text bold color="cyan">📂 Resume Previous Conversation</Text>
      <Text dimColor>Use ↑/↓ to navigate, Enter to select, Esc/q to cancel</Text>
      <Text> </Text>
      
      {sessions.map((session, index) => {
        const isSelected = index === selectedIndex;
        const shortId = session.sessionId.substring(0, 8);
        
        return (
          <Box key={session.sessionId} marginBottom={1}>
            <Text color={isSelected ? 'black' : undefined} backgroundColor={isSelected ? 'cyan' : undefined}>
              {isSelected ? '► ' : '  '}
              {shortId}... ({session.messageCount} messages) - {formatDate(session.lastActivity)}
            </Text>
          </Box>
        );
      })}
      
      <Text> </Text>
      <Text dimColor>
        Created: {sessions[selectedIndex] ? sessions[selectedIndex].createdAt.toLocaleString() : ''}
      </Text>
      <Text dimColor>
        Last Activity: {sessions[selectedIndex] ? sessions[selectedIndex].lastActivity.toLocaleString() : ''}
      </Text>
    </Box>
  );
};
