import React from 'react';
import { Box, Text } from 'ink';
import { Message } from '@powersteer/common';

/**
 * MessageView component props
 */
interface MessageViewProps {
  messages: Message[];
}

/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp: Date): string {
  return timestamp.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

/**
 * Get message type display info
 */
function getMessageTypeInfo(type: Message['type']) {
  switch (type) {
    case 'agent_request':
      return { label: 'AGENT', color: 'blue' as const };
    case 'human_response':
      return { label: 'HUMAN', color: 'green' as const };
    default:
      return { label: 'SYSTEM', color: 'gray' as const };
  }
}

/**
 * Individual message component
 */
const MessageItem: React.FC<{ message: Message }> = ({ message }) => {
  const typeInfo = getMessageTypeInfo(message.type);
  const timestamp = formatTimestamp(message.timestamp);

  return (
    <Box flexDirection="column" marginBottom={1}>
      {/* Message Header */}
      <Box>
        <Text color={typeInfo.color} bold>
          [{typeInfo.label}]
        </Text>
        <Text dimColor> {timestamp}</Text>
        {message.sessionId && (
          <Text dimColor> (Session: {message.sessionId.slice(0, 8)}...)</Text>
        )}
      </Box>

      {/* Message Content */}
      <Box paddingLeft={2}>
        <Text wrap="wrap">{message.content}</Text>
      </Box>
    </Box>
  );
};

/**
 * MessageView component - displays message history
 */
export const MessageView: React.FC<MessageViewProps> = ({ messages }) => {
  if (messages.length === 0) {
    return (
      <Box justifyContent="center" alignItems="center" flexGrow={1}>
        <Text dimColor>No messages yet. Waiting for agent requests...</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" flexGrow={1} paddingX={1}>
      {messages.map((message) => (
        <MessageItem key={message.id} message={message} />
      ))}
    </Box>
  );
};
