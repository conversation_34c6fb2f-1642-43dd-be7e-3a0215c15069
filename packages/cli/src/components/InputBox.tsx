import React, { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';

/**
 * InputBox component props
 */
interface InputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  onCommand: (command: string) => boolean;
  disabled?: boolean;
  placeholder?: string;
}

/**
 * InputBox component - handles user input with command support
 */
export const InputBox: React.FC<InputBoxProps> = ({
  value,
  onChange,
  onSubmit,
  onCommand,
  disabled = false,
  placeholder = 'Type your response...',
}) => {
  const [cursorPosition, setCursorPosition] = useState(0);

  /**
   * Handle keyboard input
   */
  useInput(
    useCallback(
      (input, key) => {
        if (disabled) {
          return;
        }

        if (key.return) {
          // Handle Enter key
          if (value.trim()) {
            // Check if it's a command
            if (value.startsWith('/')) {
              const handled = onCommand(value.trim());
              if (handled) {
                onChange('');
                setCursorPosition(0);
                return;
              }
            }
            
            // Submit the response
            onSubmit(value.trim());
          }
          return;
        }

        if (key.backspace || key.delete) {
          // Handle backspace/delete
          if (cursorPosition > 0) {
            const newValue = value.slice(0, cursorPosition - 1) + value.slice(cursorPosition);
            onChange(newValue);
            setCursorPosition(cursorPosition - 1);
          }
          return;
        }

        if (key.leftArrow) {
          // Move cursor left
          setCursorPosition(Math.max(0, cursorPosition - 1));
          return;
        }

        if (key.rightArrow) {
          // Move cursor right
          setCursorPosition(Math.min(value.length, cursorPosition + 1));
          return;
        }

        if (key.ctrl) {
          // Handle Ctrl combinations
          if (input === 'a') {
            // Ctrl+A - select all (move cursor to start)
            setCursorPosition(0);
            return;
          }
          if (input === 'e') {
            // Ctrl+E - move cursor to end
            setCursorPosition(value.length);
            return;
          }
          if (input === 'u') {
            // Ctrl+U - clear line
            onChange('');
            setCursorPosition(0);
            return;
          }
          return;
        }

        // Handle regular character input
        if (input && !key.meta && !key.ctrl) {
          const newValue = value.slice(0, cursorPosition) + input + value.slice(cursorPosition);
          onChange(newValue);
          setCursorPosition(cursorPosition + 1);
        }
      },
      [value, cursorPosition, onChange, onSubmit, onCommand, disabled]
    )
  );

  /**
   * Update cursor position when value changes externally
   */
  React.useEffect(() => {
    setCursorPosition(Math.min(cursorPosition, value.length));
  }, [value, cursorPosition]);

  /**
   * Render input with cursor
   */
  const renderInputWithCursor = () => {
    if (disabled) {
      return <Text dimColor>{placeholder}</Text>;
    }

    if (value.length === 0) {
      return (
        <Text>
          <Text backgroundColor="white" color="black">
            {' '}
          </Text>
          <Text dimColor>{placeholder}</Text>
        </Text>
      );
    }

    const beforeCursor = value.slice(0, cursorPosition);
    const atCursor = value[cursorPosition] || ' ';
    const afterCursor = value.slice(cursorPosition + 1);

    return (
      <Text>
        {beforeCursor}
        <Text backgroundColor="white" color="black">
          {atCursor}
        </Text>
        {afterCursor}
      </Text>
    );
  };

  return (
    <Box flexDirection="column">
      {/* Input prompt */}
      <Box>
        <Text bold color={disabled ? 'gray' : 'green'}>
          {'> '}
        </Text>
        {renderInputWithCursor()}
      </Box>

      {/* Help text */}
      <Box marginTop={1}>
        <Text dimColor>
          {disabled
            ? 'Input disabled - waiting for connection or request'
            : 'Press Enter to send, /exit to quit, Ctrl+U to clear'}
        </Text>
      </Box>
    </Box>
  );
};
