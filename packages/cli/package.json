{"name": "@powersteer/cli", "version": "1.0.0", "description": "Powersteer CLI for human-agent interaction", "type": "module", "main": "./build/index.js", "types": "./build/index.d.ts", "bin": {"powersteer": "./build/cli.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node build/cli.js", "test": "vitest run", "test:watch": "vitest", "clean": "rm -rf build"}, "dependencies": {"@powersteer/common": "file:../common", "ink": "^4.4.1", "react": "^18.2.0", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"@types/react": "^18.2.45", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.10", "ink-testing-library": "^3.0.0"}, "files": ["build"]}