{"version": "1.6.1", "results": [[":src/components/MessageView.test.tsx", {"duration": 30, "failed": false}], [":src/components/AgentSummary.test.tsx", {"duration": 49, "failed": false}], [":src/components/ConnectionStatus.test.tsx", {"duration": 22, "failed": false}], [":src/components/InputBox.test.tsx", {"duration": 19, "failed": false}], [":src/integration/e2e.test.ts", {"duration": 10015, "failed": true}], [":src/websocket/WebSocketClient.test.ts", {"duration": 13, "failed": false}], [":src/integration/cli.integration.test.tsx", {"duration": 71, "failed": false}], [":src/tests/error-recovery.test.ts", {"duration": 1108, "failed": false}]]}