import { jsx as _jsx } from "react/jsx-runtime";
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render } from 'ink-testing-library';
import { PowersteerApp } from '../components/PowersteerApp.js';
import { WebSocketClient } from '../websocket/WebSocketClient.js';
// Mock WebSocket to avoid actual network connections
vi.mock('ws');
// Mock the useInput hook to avoid stdin.ref issues in tests
vi.mock('ink', async () => {
    const actual = await vi.importActual('ink');
    return {
        ...actual,
        useInput: vi.fn(),
    };
});
describe('CLI Integration Tests', () => {
    const defaultConfig = {
        serverUrl: 'ws://localhost:8082',
        reconnectInterval: 1000,
        maxReconnectAttempts: 3,
    };
    beforeEach(() => {
        vi.clearAllMocks();
    });
    describe('Application Initialization', () => {
        it('should render CLI application with initial state', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            expect(output).toContain('Powersteer CLI');
            expect(output).toContain('Human-in-the-loop Agent Communication');
        });
        it('should display connection status section', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            expect(output).toContain('Connecting');
            expect(output).toContain('ws://localhost:8082');
        });
        it('should display main UI sections', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            expect(output).toContain('Message History');
            expect(output).toContain('Current Request');
            expect(output).toContain('No messages yet');
            expect(output).toContain('No active request');
        });
    });
    describe('Input Handling', () => {
        it('should display input box with disabled state initially', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            expect(output).toContain('Input disabled');
            expect(output).toContain('waiting for connection or request');
        });
        it('should show help text for user guidance', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            expect(output).toContain('Press Ctrl+C or type /exit to quit');
        });
    });
    describe('WebSocket Client Integration', () => {
        it('should create WebSocket client instance', () => {
            // Test that WebSocketClient can be instantiated with config
            const client = new WebSocketClient({
                url: 'ws://localhost:8082',
                reconnectInterval: 1000,
                maxReconnectAttempts: 3,
            });
            expect(client).toBeDefined();
            expect(typeof client.connect).toBe('function');
            expect(typeof client.disconnect).toBe('function');
            expect(typeof client.sendHumanResponse).toBe('function');
        });
        it('should handle configuration properly', () => {
            const customConfig = {
                serverUrl: 'ws://custom-server:9000',
                reconnectInterval: 5000,
                maxReconnectAttempts: 5,
            };
            const { lastFrame } = render(_jsx(PowersteerApp, { config: customConfig }));
            const output = lastFrame();
            expect(output).toContain('ws://custom-server:9000');
        });
    });
    describe('Component Integration', () => {
        it('should integrate all major components', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            // Check that all major components are rendered
            expect(output).toContain('Powersteer CLI'); // Header
            expect(output).toContain('Connecting'); // ConnectionStatus
            expect(output).toContain('Message History'); // MessageView
            expect(output).toContain('Current Request'); // AgentSummary
            expect(output).toContain('> '); // InputBox
        });
        it('should handle different server URLs in display', () => {
            const customConfig = {
                ...defaultConfig,
                serverUrl: 'wss://secure-server:443',
            };
            const { lastFrame } = render(_jsx(PowersteerApp, { config: customConfig }));
            const output = lastFrame();
            expect(output).toContain('wss://secure-server:443');
        });
    });
    describe('Error Scenarios', () => {
        it('should handle invalid configuration gracefully', () => {
            const invalidConfig = {
                serverUrl: '', // Invalid empty URL
                reconnectInterval: -1, // Invalid negative interval
                maxReconnectAttempts: 0, // Invalid zero attempts
            };
            // Should not throw an error when rendering with invalid config
            expect(() => {
                render(_jsx(PowersteerApp, { config: invalidConfig }));
            }).not.toThrow();
        });
        it('should display appropriate messages for different states', () => {
            const { lastFrame } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            const output = lastFrame();
            // Should show appropriate messages for initial state
            expect(output).toContain('No messages yet');
            expect(output).toContain('No active request');
            expect(output).toContain('Input disabled');
        });
    });
    describe('CLI Responsiveness', () => {
        it('should render without blocking', () => {
            const startTime = Date.now();
            render(_jsx(PowersteerApp, { config: defaultConfig }));
            const renderTime = Date.now() - startTime;
            // Should render quickly (under 100ms)
            expect(renderTime).toBeLessThan(100);
        });
        it('should handle multiple renders without issues', () => {
            const { rerender } = render(_jsx(PowersteerApp, { config: defaultConfig }));
            // Should be able to re-render multiple times without errors
            expect(() => {
                for (let i = 0; i < 5; i++) {
                    rerender(_jsx(PowersteerApp, { config: defaultConfig }));
                }
            }).not.toThrow();
        });
    });
});
//# sourceMappingURL=cli.integration.test.js.map