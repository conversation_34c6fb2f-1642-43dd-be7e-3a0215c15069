import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
/**
 * Message history storage using JSONL format
 */
export class MessageHistoryStorage {
    basePath;
    constructor(basePath = '/tmp') {
        this.basePath = basePath;
    }
    /**
     * Get the file path for a session's message history
     */
    getSessionFilePath(sessionId) {
        return `${this.basePath}/powersteer_${sessionId}.jsonl`;
    }
    /**
     * Get the file path for session metadata
     */
    getMetadataFilePath() {
        return `${this.basePath}/powersteer_sessions.jsonl`;
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(filePath) {
        const dir = dirname(filePath);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
    }
    /**
     * Save a message to the session's JSONL file
     */
    saveMessage(message) {
        // Skip messages without sessionId
        if (!message.sessionId) {
            console.warn('Skipping message without sessionId:', message.id);
            return;
        }
        const filePath = this.getSessionFilePath(message.sessionId);
        this.ensureDirectoryExists(filePath);
        const entry = {
            id: message.id,
            type: message.type,
            content: message.content,
            timestamp: message.timestamp.toISOString(),
            sessionId: message.sessionId,
        };
        const jsonLine = JSON.stringify(entry) + '\n';
        try {
            writeFileSync(filePath, jsonLine, { flag: 'a' });
            this.updateSessionMetadata(message.sessionId);
        }
        catch (error) {
            console.error('Failed to save message to history:', error);
        }
    }
    /**
     * Load message history for a session
     */
    loadMessages(sessionId) {
        const filePath = this.getSessionFilePath(sessionId);
        if (!existsSync(filePath)) {
            return [];
        }
        try {
            const content = readFileSync(filePath, 'utf8');
            const lines = content.trim().split('\n').filter(line => line.trim());
            return lines.map(line => {
                const entry = JSON.parse(line);
                return {
                    id: entry.id,
                    type: entry.type,
                    content: entry.content,
                    timestamp: new Date(entry.timestamp),
                    sessionId: entry.sessionId,
                };
            });
        }
        catch (error) {
            console.error('Failed to load message history:', error);
            return [];
        }
    }
    /**
     * Update session metadata
     */
    updateSessionMetadata(sessionId) {
        const metadataPath = this.getMetadataFilePath();
        this.ensureDirectoryExists(metadataPath);
        // Load existing metadata
        const existingMetadata = this.loadSessionsMetadata();
        // Find or create session metadata
        let sessionMeta = existingMetadata.find(meta => meta.sessionId === sessionId);
        if (!sessionMeta) {
            sessionMeta = {
                sessionId,
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                messageCount: 1,
            };
            existingMetadata.push(sessionMeta);
        }
        else {
            sessionMeta.lastActivity = new Date().toISOString();
            sessionMeta.messageCount += 1;
        }
        // Write all metadata back to file
        try {
            const content = existingMetadata.map(meta => JSON.stringify(meta)).join('\n') + '\n';
            writeFileSync(metadataPath, content);
        }
        catch (error) {
            console.error('Failed to update session metadata:', error);
        }
    }
    /**
     * Load all sessions metadata
     */
    loadSessionsMetadata() {
        const metadataPath = this.getMetadataFilePath();
        if (!existsSync(metadataPath)) {
            return [];
        }
        try {
            const content = readFileSync(metadataPath, 'utf8');
            const lines = content.trim().split('\n').filter(line => line.trim());
            return lines.map(line => JSON.parse(line));
        }
        catch (error) {
            console.error('Failed to load sessions metadata:', error);
            return [];
        }
    }
    /**
     * Get available session IDs with their metadata
     */
    getAvailableSessions() {
        const metadata = this.loadSessionsMetadata();
        return metadata.map(meta => ({
            sessionId: meta.sessionId,
            createdAt: new Date(meta.createdAt),
            lastActivity: new Date(meta.lastActivity),
            messageCount: meta.messageCount,
        })).sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
    }
    /**
     * Check if a session has stored messages
     */
    hasSession(sessionId) {
        const filePath = this.getSessionFilePath(sessionId);
        return existsSync(filePath);
    }
}
//# sourceMappingURL=MessageHistoryStorage.js.map