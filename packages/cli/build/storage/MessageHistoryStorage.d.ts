import { Message } from '@powersteer/common';
/**
 * Session metadata for tracking sessions
 */
interface SessionMetadata {
    sessionId: string;
    createdAt: string;
    lastActivity: string;
    messageCount: number;
}
/**
 * Message history storage using JSONL format
 */
export declare class MessageHistoryStorage {
    private readonly basePath;
    constructor(basePath?: string);
    /**
     * Get the file path for a session's message history
     */
    private getSessionFilePath;
    /**
     * Get the file path for session metadata
     */
    private getMetadataFilePath;
    /**
     * Ensure directory exists
     */
    private ensureDirectoryExists;
    /**
     * Save a message to the session's JSONL file
     */
    saveMessage(message: Message): void;
    /**
     * Load message history for a session
     */
    loadMessages(sessionId: string): Message[];
    /**
     * Update session metadata
     */
    private updateSessionMetadata;
    /**
     * Load all sessions metadata
     */
    loadSessionsMetadata(): SessionMetadata[];
    /**
     * Get available session IDs with their metadata
     */
    getAvailableSessions(): Array<{
        sessionId: string;
        createdAt: Date;
        lastActivity: Date;
        messageCount: number;
    }>;
    /**
     * Check if a session has stored messages
     */
    hasSession(sessionId: string): boolean;
}
export {};
//# sourceMappingURL=MessageHistoryStorage.d.ts.map