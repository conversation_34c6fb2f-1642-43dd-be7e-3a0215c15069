import { EventEmitter } from 'events';
import { HumanResponse } from '@powersteer/common';
import { AgentRequest } from '../components/PowersteerApp.js';
/**
 * WebSocket client configuration
 */
export interface WebSocketClientConfig {
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    sessionId?: string;
}
/**
 * Connection status type
 */
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
/**
 * WebSocket client events
 */
interface WebSocketClientEvents {
    connectionChange: (status: ConnectionStatus) => void;
    agentRequest: (request: AgentRequest) => void;
    error: (error: Error) => void;
}
/**
 * WebSocket client for CLI communication with server
 */
export declare class WebSocketClient extends EventEmitter {
    private ws;
    private config;
    private reconnectAttempts;
    private reconnectTimer;
    private isConnecting;
    private shouldReconnect;
    private pingTimer;
    private lastPongReceived;
    private readonly PING_INTERVAL;
    private readonly PONG_TIMEOUT;
    private currentSessionId;
    constructor(config: WebSocketClientConfig);
    /**
     * Connect to WebSocket server
     */
    connect(): Promise<void>;
    /**
     * Disconnect from WebSocket server
     */
    disconnect(): void;
    /**
     * Send human response to server
     */
    sendHumanResponse(response: HumanResponse): Promise<void>;
    /**
     * Setup WebSocket event handlers
     */
    private setupEventHandlers;
    /**
     * Handle incoming WebSocket messages
     */
    private handleMessage;
    /**
     * Schedule reconnection attempt with exponential backoff
     */
    private scheduleReconnect;
    /**
     * Clear reconnection timer
     */
    private clearReconnectTimer;
    /**
     * Start ping timer for connection health monitoring
     */
    private startPingTimer;
    /**
     * Send connect message to join a session
     */
    private sendConnectMessage;
    /**
     * Clear ping timer
     */
    private clearPingTimer;
    /**
     * Type-safe event emitter methods
     */
    on<K extends keyof WebSocketClientEvents>(event: K, listener: WebSocketClientEvents[K]): this;
    emit<K extends keyof WebSocketClientEvents>(event: K, ...args: Parameters<WebSocketClientEvents[K]>): boolean;
}
export {};
//# sourceMappingURL=WebSocketClient.d.ts.map