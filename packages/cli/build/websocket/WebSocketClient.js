import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { withErrorHandling } from '@powersteer/common';
import { v4 as uuidv4 } from 'uuid';
/**
 * WebSocket client for CLI communication with server
 */
export class WebSocketClient extends EventEmitter {
    ws = null;
    config;
    reconnectAttempts = 0;
    reconnectTimer = null;
    isConnecting = false;
    shouldReconnect = true;
    pingTimer = null;
    lastPongReceived = Date.now();
    PING_INTERVAL = 30000; // 30 seconds
    PONG_TIMEOUT = 10000; // 10 seconds
    currentSessionId = null;
    constructor(config) {
        super();
        this.config = config;
        this.currentSessionId = config.sessionId || null;
    }
    /**
     * Connect to WebSocket server
     */
    async connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        this.isConnecting = true;
        this.shouldReconnect = true;
        this.emit('connectionChange', 'connecting');
        try {
            this.ws = new WebSocket(this.config.url);
            this.setupEventHandlers();
        }
        catch (error) {
            this.isConnecting = false;
            this.emit('connectionChange', 'error');
            this.emit('error', error);
            this.scheduleReconnect();
        }
    }
    /**
     * Disconnect from WebSocket server
     */
    disconnect() {
        this.shouldReconnect = false;
        this.clearReconnectTimer();
        this.clearPingTimer();
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.emit('connectionChange', 'disconnected');
    }
    /**
     * Send human response to server
     */
    async sendHumanResponse(response) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket not connected');
        }
        if (!this.currentSessionId) {
            throw new Error('No active session - cannot send response');
        }
        const message = {
            type: 'human_response',
            sessionId: this.currentSessionId,
            payload: response,
            timestamp: new Date().toISOString(),
        };
        return new Promise((resolve, reject) => {
            this.ws.send(JSON.stringify(message), (error) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve();
                }
            });
        });
    }
    /**
     * Setup WebSocket event handlers
     */
    setupEventHandlers() {
        if (!this.ws)
            return;
        this.ws.on('open', () => {
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.lastPongReceived = Date.now();
            this.startPingTimer();
            // Send connect message to join the most recent session
            this.sendConnectMessage();
            this.emit('connectionChange', 'connected');
        });
        this.ws.on('message', (data) => {
            this.handleMessage(data).catch(error => {
                this.emit('error', error);
            });
        });
        this.ws.on('close', (code, reason) => {
            this.isConnecting = false;
            this.ws = null;
            this.clearPingTimer();
            console.log(`WebSocket connection closed: code=${code}, reason=${reason || 'No reason provided'}`);
            if (this.shouldReconnect) {
                this.emit('connectionChange', 'disconnected');
                this.scheduleReconnect();
            }
        });
        this.ws.on('error', (error) => {
            this.isConnecting = false;
            this.emit('connectionChange', 'error');
            this.emit('error', error);
            if (this.shouldReconnect) {
                this.scheduleReconnect();
            }
        });
    }
    /**
     * Handle incoming WebSocket messages
     */
    handleMessage = withErrorHandling(async (data) => {
        try {
            const message = JSON.parse(data.toString());
            switch (message.type) {
                case 'agent_request':
                    // Store session ID for future responses
                    this.currentSessionId = message.sessionId;
                    // Add ID to the base agent request for CLI usage
                    const baseRequest = message.payload;
                    const agentRequest = {
                        ...baseRequest,
                        id: uuidv4(),
                    };
                    this.emit('agentRequest', agentRequest);
                    break;
                case 'session_update':
                    // Handle session updates (connection confirmations, etc.)
                    console.log(`Session update: ${message.message} (Session: ${message.sessionId})`);
                    this.currentSessionId = message.sessionId;
                    break;
                case 'error':
                    // Handle error messages from server
                    const errorMsg = message.error?.message || message.message || 'Unknown error';
                    console.error('Server error:', errorMsg);
                    if (message.error?.details) {
                        console.error('Error details:', message.error.details);
                    }
                    break;
                case 'pong':
                    // Update last pong received time for connection health monitoring
                    this.lastPongReceived = Date.now();
                    break;
                default:
                    console.warn('Unknown message type:', message.type);
            }
        }
        catch (error) {
            this.emit('error', new Error(`Failed to parse message: ${error}`));
        }
    });
    /**
     * Schedule reconnection attempt with exponential backoff
     */
    scheduleReconnect() {
        if (!this.shouldReconnect || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            this.emit('connectionChange', 'error');
            console.error(`Max reconnection attempts (${this.config.maxReconnectAttempts}) reached`);
            return;
        }
        this.clearReconnectTimer();
        this.reconnectAttempts++;
        // Exponential backoff with jitter
        const baseDelay = this.config.reconnectInterval;
        const exponentialDelay = baseDelay * Math.pow(2, this.reconnectAttempts - 1);
        const jitter = Math.random() * 1000; // Add up to 1 second of jitter
        const delay = Math.min(exponentialDelay + jitter, 30000); // Cap at 30 seconds
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${Math.round(delay)}ms`);
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, delay);
    }
    /**
     * Clear reconnection timer
     */
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    /**
     * Start ping timer for connection health monitoring
     */
    startPingTimer() {
        this.clearPingTimer();
        this.pingTimer = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                // Check if we received a pong recently
                const timeSinceLastPong = Date.now() - this.lastPongReceived;
                if (timeSinceLastPong > this.PING_INTERVAL + this.PONG_TIMEOUT) {
                    console.warn('Connection appears to be dead, forcing reconnection');
                    this.ws.close();
                    return;
                }
                // Send ping
                this.ws.send(JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() }));
            }
        }, this.PING_INTERVAL);
    }
    /**
     * Send connect message to join a session
     */
    sendConnectMessage() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN)
            return;
        const connectMessage = {
            type: 'connect',
            clientType: 'cli',
            sessionId: this.currentSessionId,
            timestamp: new Date().toISOString(),
        };
        console.log('Sending connect message:', JSON.stringify(connectMessage));
        this.ws.send(JSON.stringify(connectMessage));
    }
    /**
     * Clear ping timer
     */
    clearPingTimer() {
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = null;
        }
    }
    /**
     * Type-safe event emitter methods
     */
    on(event, listener) {
        return super.on(event, listener);
    }
    emit(event, ...args) {
        return super.emit(event, ...args);
    }
}
//# sourceMappingURL=WebSocketClient.js.map