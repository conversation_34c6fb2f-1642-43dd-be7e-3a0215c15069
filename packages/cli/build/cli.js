#!/usr/bin/env node
import React from 'react';
import { render } from 'ink';
import { PowersteerApp } from './components/PowersteerApp.js';
import { withErrorHandling } from '@powersteer/common';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { parse as parseYaml } from 'yaml';
import { v4 as uuidv4 } from 'uuid';
/**
 * Default CLI configuration
 */
const defaultConfig = {
    serverUrl: 'ws://localhost:8080',
    reconnectInterval: 3000,
    maxReconnectAttempts: 10,
};
/**
 * Load configuration from config.yaml file
 */
function loadConfigFile() {
    const configPath = join(process.cwd(), 'config.yaml');
    console.log('DEBUG: Looking for config.yaml at:', configPath);
    console.log('DEBUG: Config file exists:', existsSync(configPath));
    if (!existsSync(configPath)) {
        console.log('DEBUG: config.yaml not found, using defaults');
        return {};
    }
    try {
        const configContent = readFileSync(configPath, 'utf8');
        const configFile = parseYaml(configContent);
        const config = {
            systemPrompt: configFile.systemPrompt,
            userPrompt: configFile.userPrompt,
            serverUrl: configFile.serverUrl,
            reconnectInterval: configFile.reconnectInterval,
            maxReconnectAttempts: configFile.maxReconnectAttempts,
            sessionId: configFile.sessionId,
        };
        // Debug: Log loaded config
        console.log('DEBUG: Loaded config from config.yaml:', JSON.stringify(config, null, 2));
        return config;
    }
    catch (error) {
        console.error('Warning: Failed to load config.yaml:', error);
        return {};
    }
}
/**
 * Parse command line arguments
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const config = {};
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        const nextArg = args[i + 1];
        switch (arg) {
            case '--server-url':
            case '-s':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.serverUrl = nextArg;
                    i++;
                }
                break;
            case '--reconnect-interval':
                if (nextArg && !nextArg.startsWith('-')) {
                    const interval = parseInt(nextArg, 10);
                    if (!isNaN(interval) && interval > 0) {
                        config.reconnectInterval = interval;
                    }
                    i++;
                }
                break;
            case '--max-reconnect-attempts':
                if (nextArg && !nextArg.startsWith('-')) {
                    const attempts = parseInt(nextArg, 10);
                    if (!isNaN(attempts) && attempts >= 0) {
                        config.maxReconnectAttempts = attempts;
                    }
                    i++;
                }
                break;
            case '--session-id':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.sessionId = nextArg;
                    i++;
                }
                break;
            case '--help':
            case '-h':
                showHelp();
                process.exit(0);
                break;
            case '--version':
            case '-v':
                showVersion();
                process.exit(0);
                break;
        }
    }
    return config;
}
/**
 * Show help information
 */
function showHelp() {
    console.log(`
Powersteer CLI - Human-in-the-loop agent communication

Usage: powersteer [options]

Options:
  -s, --server-url <url>              WebSocket server URL (default: ws://localhost:8080)
  --session-id <id>                   Use specific session ID (default: auto-generated)
  --reconnect-interval <ms>           Reconnection interval in milliseconds (default: 3000)
  --max-reconnect-attempts <count>    Maximum reconnection attempts (default: 10)
  -h, --help                          Show this help message
  -v, --version                       Show version information

Commands:
  /exit                               Exit the CLI
  /resume                             Resume a previous conversation
  Ctrl+C                              Force exit

Examples:
  powersteer                          Connect to default server
  powersteer -s ws://localhost:9000   Connect to custom server
  powersteer --reconnect-interval 5000 --max-reconnect-attempts 5
`);
}
/**
 * Show version information
 */
function showVersion() {
    // Read version from package.json
    console.log('Powersteer CLI v1.0.0');
}
/**
 * Main CLI entry point
 */
async function main() {
    const fileConfig = loadConfigFile();
    const argConfig = parseArgs();
    // Generate session ID if not provided
    const sessionId = argConfig.sessionId || fileConfig.sessionId || uuidv4();
    const finalConfig = {
        ...defaultConfig,
        ...fileConfig,
        ...argConfig, // CLI args take highest priority
        sessionId,
    };
    console.log('DEBUG: Final merged config:', JSON.stringify(finalConfig, null, 2));
    console.log('🆔 Session ID:', sessionId);
    // Render the Ink application
    const { waitUntilExit } = render(React.createElement(PowersteerApp, { config: finalConfig }));
    // Wait for the app to exit
    await waitUntilExit();
}
/**
 * Start the CLI with error handling
 */
withErrorHandling(main)().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=cli.js.map