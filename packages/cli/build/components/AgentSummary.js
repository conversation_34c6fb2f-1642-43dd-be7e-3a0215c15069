import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString('en-US', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
    });
}
/**
 * AgentSummary component - displays current agent request details
 */
export const AgentSummary = ({ request }) => {
    if (!request) {
        return (_jsx(Box, { flexDirection: "column", paddingX: 1, flexGrow: 1, children: _jsx(Box, { justifyContent: "center", alignItems: "center", flexGrow: 1, children: _jsx(Text, { dimColor: true, children: "No active request" }) }) }));
    }
    return (_jsxs(Box, { flexDirection: "column", paddingX: 1, flexGrow: 1, children: [_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { children: [_jsx(Text, { bold: true, color: "blue", children: "Request ID:" }), _jsxs(Text, { children: [" ", request.id.slice(0, 8), "..."] })] }), _jsxs(Box, { children: [_jsx(Text, { bold: true, color: "blue", children: "Session:" }), _jsxs(Text, { children: [" ", request.sessionId.slice(0, 8), "..."] })] }), _jsxs(Box, { children: [_jsx(Text, { bold: true, color: "blue", children: "Time:" }), _jsxs(Text, { children: [" ", formatTimestamp(request.timestamp)] })] })] }), _jsxs(Box, { flexDirection: "column", flexGrow: 1, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { bold: true, color: "yellow", children: "Agent Summary:" }) }), _jsx(Box, { borderStyle: "single", paddingX: 1, paddingY: 1, flexGrow: 1, children: _jsx(Text, { wrap: "wrap", children: request.summary }) })] }), request.timeout && request.timeout !== 30000 && (_jsxs(Box, { marginTop: 1, children: [_jsx(Text, { bold: true, color: "red", children: "Timeout:" }), _jsxs(Text, { children: [" ", request.timeout, "ms"] })] })), _jsx(Box, { marginTop: 1, borderStyle: "single", paddingX: 1, children: _jsx(Text, { dimColor: true, wrap: "wrap", children: "\uD83D\uDCA1 Review the agent's summary above and provide your response in the input field below." }) })] }));
};
//# sourceMappingURL=AgentSummary.js.map