import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
/**
 * Get status display information
 */
function getStatusInfo(status) {
    switch (status) {
        case 'connecting':
            return {
                icon: '🔄',
                text: 'Connecting...',
                color: 'yellow',
            };
        case 'connected':
            return {
                icon: '✅',
                text: 'Connected',
                color: 'green',
            };
        case 'disconnected':
            return {
                icon: '❌',
                text: 'Disconnected',
                color: 'red',
            };
        case 'error':
            return {
                icon: '⚠️',
                text: 'Connection Error',
                color: 'red',
            };
        default:
            return {
                icon: '❓',
                text: 'Unknown',
                color: 'gray',
            };
    }
}
/**
 * ConnectionStatus component - displays WebSocket connection status
 */
export const ConnectionStatus = ({ status, serverUrl }) => {
    const statusInfo = getStatusInfo(status);
    return (_jsxs(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: [_jsx(Box, { flexGrow: 1, children: _jsxs(Text, { children: [statusInfo.icon, ' ', _jsx(Text, { color: statusInfo.color, bold: true, children: statusInfo.text }), ' - ', _jsx(Text, { dimColor: true, children: serverUrl })] }) }), status === 'connecting' && (_jsx(Text, { dimColor: true, children: " (Attempting to connect...)" })), status === 'disconnected' && (_jsx(Text, { dimColor: true, children: " (Will attempt to reconnect)" })), status === 'error' && (_jsx(Text, { dimColor: true, children: " (Check server and try again)" }))] }));
};
//# sourceMappingURL=ConnectionStatus.js.map