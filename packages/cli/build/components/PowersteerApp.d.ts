import React from 'react';
import { AgentRequest as BaseAgentRequest } from '@powersteer/common';
/**
 * Extended AgentRequest with ID for CLI usage
 */
export interface AgentRequest extends BaseAgentRequest {
    id: string;
}
/**
 * CLI configuration interface
 */
export interface CLIConfig {
    serverUrl: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    systemPrompt?: string;
    userPrompt?: string;
    sessionId?: string;
}
/**
 * PowersteerApp component props
 */
interface PowersteerAppProps {
    config: CLIConfig;
}
/**
 * Main Powersteer CLI application component
 */
export declare const PowersteerApp: React.FC<PowersteerAppProps>;
export {};
//# sourceMappingURL=PowersteerApp.d.ts.map