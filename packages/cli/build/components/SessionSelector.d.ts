import React from 'react';
/**
 * Session information for selection
 */
interface SessionInfo {
    sessionId: string;
    createdAt: Date;
    lastActivity: Date;
    messageCount: number;
}
/**
 * Props for SessionSelector component
 */
interface SessionSelectorProps {
    sessions: SessionInfo[];
    onSelect: (sessionId: string) => void;
    onCancel: () => void;
}
/**
 * Component for selecting a session to resume
 */
export declare const SessionSelector: React.FC<SessionSelectorProps>;
export {};
//# sourceMappingURL=SessionSelector.d.ts.map