import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
/**
 * Component for selecting a session to resume
 */
export const SessionSelector = ({ sessions, onSelect, onCancel, }) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    /**
     * <PERSON><PERSON> keyboard input for navigation
     */
    useInput((input, key) => {
        if (key.upArrow && selectedIndex > 0) {
            setSelectedIndex(selectedIndex - 1);
        }
        else if (key.downArrow && selectedIndex < sessions.length - 1) {
            setSelectedIndex(selectedIndex + 1);
        }
        else if (key.return) {
            if (sessions[selectedIndex]) {
                onSelect(sessions[selectedIndex].sessionId);
            }
        }
        else if (key.escape || input === 'q') {
            onCancel();
        }
    });
    /**
     * Format date for display
     */
    const formatDate = useCallback((date) => {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays > 0) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        }
        else if (diffHours > 0) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        }
        else {
            return 'Less than an hour ago';
        }
    }, []);
    if (sessions.length === 0) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { bold: true, color: "yellow", children: "\uD83D\uDCC2 No Previous Sessions Found" }), _jsx(Text, { children: "No conversation history files found in /tmp/" }), _jsx(Text, { dimColor: true, children: "Press any key to continue..." })] }));
    }
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { bold: true, color: "cyan", children: "\uD83D\uDCC2 Resume Previous Conversation" }), _jsx(Text, { dimColor: true, children: "Use \u2191/\u2193 to navigate, Enter to select, Esc/q to cancel" }), _jsx(Text, { children: " " }), sessions.map((session, index) => {
                const isSelected = index === selectedIndex;
                const shortId = session.sessionId.substring(0, 8);
                return (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: isSelected ? 'black' : undefined, backgroundColor: isSelected ? 'cyan' : undefined, children: [isSelected ? '► ' : '  ', shortId, "... (", session.messageCount, " messages) - ", formatDate(session.lastActivity)] }) }, session.sessionId));
            }), _jsx(Text, { children: " " }), _jsxs(Text, { dimColor: true, children: ["Created: ", sessions[selectedIndex] ? sessions[selectedIndex].createdAt.toLocaleString() : ''] }), _jsxs(Text, { dimColor: true, children: ["Last Activity: ", sessions[selectedIndex] ? sessions[selectedIndex].lastActivity.toLocaleString() : ''] })] }));
};
//# sourceMappingURL=SessionSelector.js.map