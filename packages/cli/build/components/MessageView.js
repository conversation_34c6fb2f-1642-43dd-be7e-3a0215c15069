import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp) {
    return timestamp.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    });
}
/**
 * Get message type display info
 */
function getMessageTypeInfo(type) {
    switch (type) {
        case 'agent_request':
            return { label: 'AGENT', color: 'blue' };
        case 'human_response':
            return { label: 'HUMAN', color: 'green' };
        default:
            return { label: 'SYSTEM', color: 'gray' };
    }
}
/**
 * Individual message component
 */
const MessageItem = ({ message }) => {
    const typeInfo = getMessageTypeInfo(message.type);
    const timestamp = formatTimestamp(message.timestamp);
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { children: [_jsxs(Text, { color: typeInfo.color, bold: true, children: ["[", typeInfo.label, "]"] }), _jsxs(Text, { dimColor: true, children: [" ", timestamp] }), message.sessionId && (_jsxs(Text, { dimColor: true, children: [" (Session: ", message.sessionId.slice(0, 8), "...)"] }))] }), _jsx(Box, { paddingLeft: 2, children: _jsx(Text, { wrap: "wrap", children: message.content }) })] }));
};
/**
 * MessageView component - displays message history
 */
export const MessageView = ({ messages }) => {
    if (messages.length === 0) {
        return (_jsx(Box, { justifyContent: "center", alignItems: "center", flexGrow: 1, children: _jsx(Text, { dimColor: true, children: "No messages yet. Waiting for agent requests..." }) }));
    }
    return (_jsx(Box, { flexDirection: "column", flexGrow: 1, paddingX: 1, children: messages.map((message) => (_jsx(MessageItem, { message: message }, message.id))) }));
};
//# sourceMappingURL=MessageView.js.map