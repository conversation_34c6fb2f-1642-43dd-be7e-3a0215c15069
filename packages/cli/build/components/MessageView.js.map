{"version": 3, "file": "MessageView.js", "sourceRoot": "", "sources": ["../../src/components/MessageView.tsx"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAUhC;;GAEG;AACH,SAAS,eAAe,CAAC,SAAe;IACtC,OAAO,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE;QAC3C,MAAM,EAAE,KAAK;QACb,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;KAClB,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,IAAqB;IAC/C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,eAAe;YAClB,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAe,EAAE,CAAC;QACpD,KAAK,gBAAgB;YACnB,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAgB,EAAE,CAAC;QACrD;YACE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAe,EAAE,CAAC;IACvD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,WAAW,GAAmC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;IAClE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAErD,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aAEzC,MAAC,GAAG,eACF,MAAC,IAAI,IAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,wBAC7B,QAAQ,CAAC,KAAK,SACX,EACP,MAAC,IAAI,IAAC,QAAQ,wBAAG,SAAS,IAAQ,EACjC,OAAO,CAAC,SAAS,IAAI,CACpB,MAAC,IAAI,IAAC,QAAQ,kCAAa,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CACrE,IACG,EAGN,KAAC,GAAG,IAAC,WAAW,EAAE,CAAC,YACjB,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YAAE,OAAO,CAAC,OAAO,GAAQ,GACtC,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;IACtE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,CACL,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,YAC1D,KAAC,IAAI,IAAC,QAAQ,qEAAsD,GAChE,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YACjD,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CACzB,KAAC,WAAW,IAAkB,OAAO,EAAE,OAAO,IAA5B,OAAO,CAAC,EAAE,CAAsB,CACnD,CAAC,GACE,CACP,CAAC;AACJ,CAAC,CAAC"}