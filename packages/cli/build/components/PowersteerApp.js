import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useApp, useInput } from 'ink';
import { MessageView } from './MessageView.js';
import { AgentSummary } from './AgentSummary.js';
import { InputBox } from './InputBox.js';
import { ConnectionStatus } from './ConnectionStatus.js';
import { SessionSelector } from './SessionSelector.js';
import { ErrorBoundary } from './ErrorBoundary.js';
import { WebSocketClient } from '../websocket/WebSocketClient.js';
import { MessageHistoryStorage } from '../storage/MessageHistoryStorage.js';
import { v4 as uuidv4 } from 'uuid';
/**
 * Main Powersteer CLI application component
 */
export const PowersteerApp = ({ config }) => {
    const { exit } = useApp();
    const [wsClient, setWsClient] = useState(null);
    const [messageStorage] = useState(() => new MessageHistoryStorage());
    const [state, setState] = useState({
        messages: [],
        currentRequest: null,
        connectionStatus: 'connecting',
        inputValue: '',
        isWaitingForResponse: false,
        resumeMode: false,
        availableSessions: [],
    });
    /**
     * Handle WebSocket connection events
     */
    const handleConnectionChange = useCallback((status) => {
        setState(prev => ({ ...prev, connectionStatus: status }));
        // Handle connection recovery
        if (status === 'error') {
            console.error('WebSocket connection failed, attempting recovery...');
            // Could implement additional recovery logic here
        }
    }, []);
    /**
     * Handle incoming agent requests
     */
    const handleAgentRequest = useCallback((request) => {
        const message = {
            id: request.id,
            type: 'agent_request',
            content: request.summary,
            timestamp: new Date(),
            sessionId: request.sessionId,
        };
        // Save message to persistent storage
        messageStorage.saveMessage(message);
        setState(prev => ({
            ...prev,
            messages: [...prev.messages, message],
            currentRequest: request,
            isWaitingForResponse: false,
        }));
    }, [messageStorage]);
    /**
     * Handle sending human response
     */
    const handleSendResponse = useCallback(async (responseText) => {
        if (!wsClient || !state.currentRequest) {
            return;
        }
        // Debug: Log config values
        console.log('DEBUG: Config systemPrompt:', config.systemPrompt);
        console.log('DEBUG: Config userPrompt:', config.userPrompt);
        const response = {
            sessionId: state.currentRequest.sessionId,
            response: responseText,
            systemPrompt: config.systemPrompt,
            userPrompt: config.userPrompt,
            timestamp: new Date().toISOString(),
        };
        // Debug: Log the response being sent
        console.log('DEBUG: Sending response:', JSON.stringify(response, null, 2));
        try {
            await wsClient.sendHumanResponse(response);
            const message = {
                id: `response-${state.currentRequest.id}`,
                type: 'human_response',
                content: responseText,
                timestamp: new Date(),
                sessionId: response.sessionId,
            };
            // Save message to persistent storage
            messageStorage.saveMessage(message);
            setState(prev => ({
                ...prev,
                messages: [...prev.messages, message],
                currentRequest: null,
                inputValue: '',
                isWaitingForResponse: true,
            }));
        }
        catch (error) {
            // Handle error - could add error message to state
            console.error('Failed to send response:', error);
        }
    }, [wsClient, state.currentRequest, messageStorage]);
    /**
     * Handle input changes
     */
    const handleInputChange = useCallback((value) => {
        setState(prev => ({ ...prev, inputValue: value }));
    }, []);
    /**
     * Handle special commands
     */
    const handleCommand = useCallback((command) => {
        if (command === '/exit') {
            exit();
            return true;
        }
        if (command === '/resume') {
            const sessions = messageStorage.getAvailableSessions();
            setState(prev => ({
                ...prev,
                resumeMode: true,
                availableSessions: sessions,
                inputValue: '',
            }));
            return true;
        }
        return false;
    }, [exit, messageStorage]);
    /**
     * Handle session selection for resume
     */
    const handleSessionSelect = useCallback((sessionId) => {
        console.log(`🔄 Resuming session: ${sessionId}`);
        // Load messages from the selected session
        const messages = messageStorage.loadMessages(sessionId);
        setState(prev => ({
            ...prev,
            messages,
            resumeMode: false,
            availableSessions: [],
            inputValue: '',
        }));
        // Update the config to use the selected session ID
        // Note: This is a simplified approach. In a real app, you might want to
        // restart the WebSocket connection with the new session ID
        console.log(`📚 Loaded ${messages.length} messages from session ${sessionId}`);
    }, [messageStorage]);
    /**
     * Handle canceling session selection
     */
    const handleSessionCancel = useCallback(() => {
        setState(prev => ({
            ...prev,
            resumeMode: false,
            availableSessions: [],
            inputValue: '',
        }));
    }, []);
    /**
     * Handle WebSocket errors
     */
    const handleWebSocketError = useCallback((error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({
            ...prev,
            connectionStatus: 'error',
            messages: [...prev.messages, {
                    id: uuidv4(),
                    type: 'system',
                    content: `Connection error: ${error.message}`,
                    timestamp: new Date(),
                }]
        }));
    }, []);
    /**
     * Load existing message history on startup
     */
    useEffect(() => {
        if (config.sessionId && messageStorage.hasSession(config.sessionId)) {
            const existingMessages = messageStorage.loadMessages(config.sessionId);
            console.log(`📚 Loaded ${existingMessages.length} messages from session ${config.sessionId}`);
            setState(prev => ({
                ...prev,
                messages: existingMessages,
            }));
        }
    }, [config.sessionId, messageStorage]);
    /**
     * Initialize WebSocket client
     */
    useEffect(() => {
        const client = new WebSocketClient({
            url: config.serverUrl,
            reconnectInterval: config.reconnectInterval,
            maxReconnectAttempts: config.maxReconnectAttempts,
            sessionId: config.sessionId,
        });
        client.on('connectionChange', handleConnectionChange);
        client.on('agentRequest', handleAgentRequest);
        client.on('error', handleWebSocketError);
        client.connect();
        setWsClient(client);
        return () => {
            client.disconnect();
        };
    }, [config, handleConnectionChange, handleAgentRequest, handleWebSocketError]);
    /**
     * Handle global input for exit commands
     */
    useInput((input, key) => {
        if (key.ctrl && input === 'c') {
            exit();
        }
    });
    return (_jsx(ErrorBoundary, { onError: (error, errorInfo) => {
            console.error('PowersteerApp Error:', error, errorInfo);
        }, children: _jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsx(Text, { bold: true, color: "blue", children: "Powersteer CLI - Human-in-the-loop Agent Communication" }) }), _jsx(ConnectionStatus, { status: state.connectionStatus, serverUrl: config.serverUrl }), state.resumeMode && (_jsx(SessionSelector, { sessions: state.availableSessions, onSelect: handleSessionSelect, onCancel: handleSessionCancel })), !state.resumeMode && (_jsxs(Box, { flexDirection: "row", marginBottom: 1, children: [_jsxs(Box, { flexDirection: "column", flexGrow: 1, marginRight: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: _jsx(Text, { bold: true, children: "Current Request" }) }), _jsx(AgentSummary, { request: state.currentRequest })] }), _jsxs(Box, { flexDirection: "column", width: 30, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: _jsx(Text, { bold: true, children: "Status" }) }), _jsxs(Box, { paddingX: 1, children: [_jsxs(Text, { children: ["Messages: ", _jsx(Text, { color: "cyan", children: state.messages.length })] }), state.messages.length > 20 && (_jsx(Text, { dimColor: true, children: "Showing last 10 interactions" }))] })] })] })), !state.resumeMode && (_jsx(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: _jsx(InputBox, { value: state.inputValue, onChange: handleInputChange, onSubmit: handleSendResponse, onCommand: handleCommand, disabled: !state.currentRequest || state.connectionStatus !== 'connected', placeholder: state.currentRequest
                            ? "Type your response..."
                            : state.isWaitingForResponse
                                ? "Waiting for next request..."
                                : "No active request" }) })), !state.resumeMode && (_jsxs(Box, { flexDirection: "column", flexGrow: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: _jsx(Text, { bold: true, children: "Message History" }) }), _jsx(MessageView, { messages: state.messages.slice(-20) })] })), _jsx(Box, { justifyContent: "center", paddingY: 1, children: _jsx(Text, { dimColor: true, children: "Press Ctrl+C or type /exit to quit \u2022 /resume to load previous conversations" }) })] }) }));
};
//# sourceMappingURL=PowersteerApp.js.map