import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Component } from 'react';
import { Box, Text } from 'ink';
/**
 * React Error Boundary for CLI application
 * Catches and handles React component errors gracefully
 */
export class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error,
        };
    }
    componentDidCatch(error, errorInfo) {
        this.setState({
            error,
            errorInfo,
        });
        // Call optional error handler
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
        // Log error for debugging
        console.error('React Error Boundary caught an error:', error);
        console.error('Error Info:', errorInfo);
    }
    render() {
        if (this.state.hasError) {
            // Custom fallback UI or default error display
            if (this.props.fallback) {
                return this.props.fallback;
            }
            return (_jsxs(Box, { flexDirection: "column", padding: 2, children: [_jsx(Box, { borderStyle: "double", borderColor: "red", padding: 1, marginBottom: 1, children: _jsx(Text, { color: "red", bold: true, children: "\u26A0\uFE0F Application Error" }) }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Text, { color: "red", children: "An unexpected error occurred in the CLI application:" }), _jsx(Text, { color: "gray", wrap: "wrap", children: this.state.error?.message || 'Unknown error' })] }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Text, { bold: true, children: "Troubleshooting Steps:" }), _jsx(Text, { children: "\u2022 Check if the Powersteer server is running" }), _jsx(Text, { children: "\u2022 Verify the server URL is correct" }), _jsx(Text, { children: "\u2022 Try restarting the CLI application" }), _jsx(Text, { children: "\u2022 Check network connectivity" })] }), _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { dimColor: true, children: "Press Ctrl+C to exit and restart the application" }), process.env.NODE_ENV === 'development' && (_jsxs(Box, { flexDirection: "column", marginTop: 1, children: [_jsx(Text, { color: "yellow", children: "Development Info:" }), _jsx(Text, { color: "gray", wrap: "wrap", children: this.state.error?.stack })] }))] })] }));
        }
        return this.props.children;
    }
}
/**
 * Higher-order component for wrapping components with error boundary
 */
export function withErrorBoundary(Component, fallback, onError) {
    return function WrappedComponent(props) {
        return (_jsx(ErrorBoundary, { fallback: fallback, onError: onError, children: _jsx(Component, { ...props }) }));
    };
}
/**
 * Error recovery utilities
 */
export class ErrorRecovery {
    /**
     * Attempt to recover from WebSocket connection errors
     */
    static async recoverWebSocketConnection(client, maxAttempts = 5, baseDelay = 1000) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                await client.connect();
                return true;
            }
            catch (error) {
                console.error(`Connection attempt ${attempt} failed:`, error);
                if (attempt < maxAttempts) {
                    // Exponential backoff with jitter
                    const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        return false;
    }
    /**
     * Handle session timeout recovery
     */
    static handleSessionTimeout(sessionId, onRecover) {
        console.warn(`Session ${sessionId} timed out, attempting recovery...`);
        // Clear any pending operations
        // Reset application state
        // Attempt to reconnect
        if (onRecover) {
            onRecover();
        }
    }
    /**
     * Graceful degradation when server is unavailable
     */
    static enableOfflineMode() {
        const queuedMessages = [];
        return {
            isOffline: true,
            queuedMessages,
            addMessage: (message) => {
                queuedMessages.push({
                    ...message,
                    timestamp: new Date().toISOString(),
                    queued: true,
                });
            },
            flushMessages: () => {
                const messages = [...queuedMessages];
                queuedMessages.length = 0;
                return messages;
            },
        };
    }
}
//# sourceMappingURL=ErrorBoundary.js.map