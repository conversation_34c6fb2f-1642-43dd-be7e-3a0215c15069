import React, { Component, ReactNode } from 'react';
/**
 * Error boundary state
 */
interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: React.ErrorInfo;
}
/**
 * Error boundary props
 */
interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}
/**
 * React Error Boundary for CLI application
 * Catches and handles React component errors gracefully
 */
export declare class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(error: Error): ErrorBoundaryState;
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void;
    render(): string | number | boolean | Iterable<React.ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
}
/**
 * Higher-order component for wrapping components with error boundary
 */
export declare function withErrorBoundary<P extends object>(Component: React.ComponentType<P>, fallback?: ReactNode, onError?: (error: Error, errorInfo: React.ErrorInfo) => void): (props: P) => import("react/jsx-runtime").JSX.Element;
/**
 * Error recovery utilities
 */
export declare class ErrorRecovery {
    /**
     * Attempt to recover from WebSocket connection errors
     */
    static recoverWebSocketConnection(client: any, maxAttempts?: number, baseDelay?: number): Promise<boolean>;
    /**
     * Handle session timeout recovery
     */
    static handleSessionTimeout(sessionId: string, onRecover?: () => void): void;
    /**
     * Graceful degradation when server is unavailable
     */
    static enableOfflineMode(): {
        isOffline: boolean;
        queuedMessages: any[];
        addMessage: (message: any) => void;
        flushMessages: () => any[];
    };
}
export {};
//# sourceMappingURL=ErrorBoundary.d.ts.map