{"version": 3, "file": "SessionSelector.js", "sourceRoot": "", "sources": ["../../src/components/SessionSelector.tsx"], "names": [], "mappings": ";AAAA,OAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAqB1C;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAmC,CAAC,EAC9D,QAAQ,EACR,QAAQ,EACR,QAAQ,GACT,EAAE,EAAE;IACH,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEtD;;OAEG;IACH,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,OAAO,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACrC,gBAAgB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,GAAG,CAAC,SAAS,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,gBAAgB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5B,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACvC,QAAQ,EAAE,CAAC;QACb,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,IAAU,EAAU,EAAE;QACpD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;QAE5C,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,OAAO,GAAG,QAAQ,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;QACzD,CAAC;aAAM,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,GAAG,SAAS,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,OAAO,uBAAuB,CAAC;QACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACpC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,QAAQ,wDAAqC,EAC9D,KAAC,IAAI,+DAAoD,EACzD,KAAC,IAAI,IAAC,QAAQ,mDAAoC,IAC9C,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACpC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,0DAAuC,EAC9D,KAAC,IAAI,IAAC,QAAQ,sFAA6D,EAC3E,KAAC,IAAI,oBAAS,EAEb,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gBAC/B,MAAM,UAAU,GAAG,KAAK,KAAK,aAAa,CAAC;gBAC3C,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAElD,OAAO,CACL,KAAC,GAAG,IAAyB,YAAY,EAAE,CAAC,YAC1C,MAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aAC5F,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACxB,OAAO,WAAO,OAAO,CAAC,YAAY,mBAAe,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,IAC7E,IAJC,OAAO,CAAC,SAAS,CAKrB,CACP,CAAC;YACJ,CAAC,CAAC,EAEF,KAAC,IAAI,oBAAS,EACd,MAAC,IAAI,IAAC,QAAQ,gCACF,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,IACtF,EACP,MAAC,IAAI,IAAC,QAAQ,sCACI,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,IAC/F,IACH,CACP,CAAC;AACJ,CAAC,CAAC"}