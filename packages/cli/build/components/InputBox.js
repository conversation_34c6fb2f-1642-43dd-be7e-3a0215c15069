import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
/**
 * InputBox component - handles user input with command support
 */
export const InputBox = ({ value, onChange, onSubmit, onCommand, disabled = false, placeholder = 'Type your response...', }) => {
    const [cursorPosition, setCursorPosition] = useState(0);
    /**
     * Handle keyboard input
     */
    useInput(useCallback((input, key) => {
        if (disabled) {
            return;
        }
        if (key.return) {
            // Handle Enter key
            if (value.trim()) {
                // Check if it's a command
                if (value.startsWith('/')) {
                    const handled = onCommand(value.trim());
                    if (handled) {
                        onChange('');
                        setCursorPosition(0);
                        return;
                    }
                }
                // Submit the response
                onSubmit(value.trim());
            }
            return;
        }
        if (key.backspace || key.delete) {
            // Handle backspace/delete
            if (cursorPosition > 0) {
                const newValue = value.slice(0, cursorPosition - 1) + value.slice(cursorPosition);
                onChange(newValue);
                setCursorPosition(cursorPosition - 1);
            }
            return;
        }
        if (key.leftArrow) {
            // Move cursor left
            setCursorPosition(Math.max(0, cursorPosition - 1));
            return;
        }
        if (key.rightArrow) {
            // Move cursor right
            setCursorPosition(Math.min(value.length, cursorPosition + 1));
            return;
        }
        if (key.ctrl) {
            // Handle Ctrl combinations
            if (input === 'a') {
                // Ctrl+A - select all (move cursor to start)
                setCursorPosition(0);
                return;
            }
            if (input === 'e') {
                // Ctrl+E - move cursor to end
                setCursorPosition(value.length);
                return;
            }
            if (input === 'u') {
                // Ctrl+U - clear line
                onChange('');
                setCursorPosition(0);
                return;
            }
            return;
        }
        // Handle regular character input
        if (input && !key.meta && !key.ctrl) {
            const newValue = value.slice(0, cursorPosition) + input + value.slice(cursorPosition);
            onChange(newValue);
            setCursorPosition(cursorPosition + 1);
        }
    }, [value, cursorPosition, onChange, onSubmit, onCommand, disabled]));
    /**
     * Update cursor position when value changes externally
     */
    React.useEffect(() => {
        setCursorPosition(Math.min(cursorPosition, value.length));
    }, [value, cursorPosition]);
    /**
     * Render input with cursor
     */
    const renderInputWithCursor = () => {
        if (disabled) {
            return _jsx(Text, { dimColor: true, children: placeholder });
        }
        if (value.length === 0) {
            return (_jsxs(Text, { children: [_jsx(Text, { backgroundColor: "white", color: "black", children: ' ' }), _jsx(Text, { dimColor: true, children: placeholder })] }));
        }
        const beforeCursor = value.slice(0, cursorPosition);
        const atCursor = value[cursorPosition] || ' ';
        const afterCursor = value.slice(cursorPosition + 1);
        return (_jsxs(Text, { children: [beforeCursor, _jsx(Text, { backgroundColor: "white", color: "black", children: atCursor }), afterCursor] }));
    };
    return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { children: [_jsx(Text, { bold: true, color: disabled ? 'gray' : 'green', children: '> ' }), renderInputWithCursor()] }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { dimColor: true, children: disabled
                        ? 'Input disabled - waiting for connection or request'
                        : 'Press Enter to send, /exit to quit, Ctrl+U to clear' }) })] }));
};
//# sourceMappingURL=InputBox.js.map