import React from 'react';
/**
 * Connection status type
 */
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
/**
 * ConnectionStatus component props
 */
interface ConnectionStatusProps {
    status: ConnectionStatus;
    serverUrl: string;
}
/**
 * ConnectionStatus component - displays WebSocket connection status
 */
export declare const ConnectionStatus: React.FC<ConnectionStatusProps>;
export {};
//# sourceMappingURL=ConnectionStatus.d.ts.map