import React from 'react';
/**
 * InputBox component props
 */
interface InputBoxProps {
    value: string;
    onChange: (value: string) => void;
    onSubmit: (value: string) => void;
    onCommand: (command: string) => boolean;
    disabled?: boolean;
    placeholder?: string;
}
/**
 * InputBox component - handles user input with command support
 */
export declare const InputBox: React.FC<InputBoxProps>;
export {};
//# sourceMappingURL=InputBox.d.ts.map