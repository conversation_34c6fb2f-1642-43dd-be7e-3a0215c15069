# Powersteer - Human-in-the-Loop Agent Communication System

Powersteer is a comprehensive MCP (Model Context Protocol) server and CLI system designed to facilitate seamless communication between AI coding agents and human operators. It provides a robust, real-time interface for human oversight and intervention in automated coding workflows.

## 🚀 Features

- **Real-time Communication**: WebSocket-based bidirectional communication between agents and humans
- **MCP Protocol Compliance**: Full implementation of Model Context Protocol standards
- **Interactive CLI**: Rich terminal interface with real-time updates and error handling
- **Session Management**: Persistent sessions with timeout handling and recovery
- **Error Recovery**: Comprehensive error handling with exponential backoff and circuit breakers
- **Type Safety**: Full TypeScript implementation with strict type checking
- **Extensible Architecture**: Modular design supporting custom integrations

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn package manager
- Terminal with Unicode support (for CLI interface)

## 🛠️ Installation

### From Source

```bash
# Clone the repository
git clone https://github.com/your-org/powersteer.git
cd powersteer

# Install dependencies
npm install

# Build all packages
npm run build
```

### Package Installation

```bash
# Install CLI globally
npm install -g @powersteer/cli

# Or install server package
npm install @powersteer/server
```

## 🏗️ Architecture

Powersteer uses a dual-interface architecture:

- **🤖 AI Agents** → Connect via **MCP Protocol** → **Powersteer MCP Server**
- **👤 Humans** → Connect via **Terminal Interface** → **Powersteer CLI**

The MCP server and CLI communicate through WebSocket to enable seamless agent-human interaction.

## 🏃 Quick Start

### 1. Start Both Servers

**Option A: Integrated Server (Recommended)**
```bash
# Terminal 1: Start integrated server (WebSocket + MCP together)
npm run start:server

# Terminal 2: Start CLI interface (for humans)
npm run start:cli
```

**Option B: Standalone MCP Server (Advanced)**
```bash
# Terminal 1: Start WebSocket server only
# Note: This requires a custom WebSocket-only server (not yet implemented)

# Terminal 2: Start standalone MCP server
npm run start:mcp                           # From project root
node packages/server/build/mcp/mcp-cli.js  # Direct path
npx @powersteer/server powersteer-mcp       # If published to npm

# Terminal 3: Start CLI interface
npm run start:cli
```

> ⚠️ **Important**: Option A is recommended as it ensures proper communication between components. Option B requires additional setup.

### 2. MCP Configuration (mcp.json)

For AI clients like Claude Desktop, add this to your `mcp.json`:

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["packages/server/build/cli.js"],
      "cwd": "/path/to/your/powersteer3"
    }
  }
}
```

**Alternative configurations:**

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "npm",
      "args": ["run", "start:server"],
      "cwd": "/path/to/your/powersteer3"
    }
  }
}
```

### 3. Programmatic AI Agent Integration

For custom AI agents using MCP client libraries:

```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

// AI agent creates MCP client (connects to integrated server)
const transport = new StdioClientTransport({
  command: 'node',
  args: ['packages/server/build/cli.js'],
  cwd: '/path/to/your/powersteer3'
});

const client = new Client({
  name: 'my-ai-agent',
  version: '1.0.0'
}, { capabilities: {} });

await client.connect(transport);

// AI agent requests human input
const result = await client.request({
  method: 'tools/call',
  params: {
    name: 'request_human_input',
    arguments: {
      agentId: 'my-ai-agent',
      type: 'confirmation',
      summary: 'Should I proceed with this action?',
      timeout: 120
    }
  }
});
```

### 3. Basic Usage Flow

1. **Agent Request**: AI agent sends a request through MCP protocol
2. **Human Review**: Request appears in CLI interface for human review
3. **Human Response**: Human provides feedback, approval, or modifications
4. **Agent Continuation**: Agent receives response and continues workflow

## 📖 Usage Examples

### Example 1: Code Review Workflow

```typescript
// Agent sends code review request
const request = {
  type: 'agent_request',
  content: 'Please review this function for security vulnerabilities',
  context: {
    code: `
      function processUserInput(input: string) {
        return eval(input); // Potential security issue
      }
    `,
    file: 'src/utils/processor.ts'
  }
};
```

**Human Response in CLI:**
```
🤖 Agent Request: Please review this function for security vulnerabilities

📁 File: src/utils/processor.ts
📝 Code:
function processUserInput(input: string) {
  return eval(input); // Potential security issue
}

⚠️  SECURITY ISSUE DETECTED: Using eval() is dangerous
✅ Suggested fix: Use JSON.parse() or implement proper input validation

Your response: [REJECT] Replace eval() with safe input parsing
```

### Example 2: Database Migration Approval

```typescript
// Agent requests permission for database changes
const migrationRequest = {
  type: 'agent_request',
  content: 'Approve database migration: Add user_preferences table',
  context: {
    migration: 'CREATE TABLE user_preferences (id SERIAL, user_id INT, preferences JSONB)',
    impact: 'Low - New table, no existing data affected'
  }
};
```

**Human Response:**
```
🗄️  Database Migration Request
📋 Migration: CREATE TABLE user_preferences (id SERIAL, user_id INT, preferences JSONB)
📊 Impact: Low - New table, no existing data affected

Your response: [APPROVE] Migration looks good, proceed with deployment
```

### Example 3: Error Resolution Guidance

```typescript
// Agent encounters error and requests guidance
const errorRequest = {
  type: 'agent_request',
  content: 'Build failed with TypeScript errors. How should I proceed?',
  context: {
    errors: [
      'Property "id" is missing in type User',
      'Cannot find module "@/utils/helper"'
    ],
    branch: 'feature/user-management'
  }
};
```

**Human Response:**
```
❌ Build Error - Guidance Needed
🔧 Errors:
  • Property "id" is missing in type User
  • Cannot find module "@/utils/helper"

🌿 Branch: feature/user-management

Your response: [GUIDE] 
1. Add id field to User interface in types/user.ts
2. Check import path - should be "../utils/helper"
3. Run type check after fixes
```

## 🔧 Configuration

### Server Configuration

Create `config/server.json`:

```json
{
  "port": 3001,
  "host": "localhost",
  "cors": {
    "origin": "*",
    "credentials": true
  },
  "session": {
    "timeout": 300000,
    "cleanup_interval": 60000
  },
  "websocket": {
    "ping_interval": 30000,
    "pong_timeout": 10000
  }
}
```

### CLI Configuration

Create `config/cli.json`:

```json
{
  "server_url": "ws://localhost:3001",
  "reconnect": {
    "max_attempts": 5,
    "initial_delay": 1000,
    "max_delay": 30000
  },
  "ui": {
    "theme": "default",
    "show_timestamps": true,
    "auto_scroll": true
  }
}
```

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI Agent      │    │  MCP Server     │    │  Human CLI      │
│                 │    │                 │    │                 │
│ • Code Analysis │◄──►│ • WebSocket Hub │◄──►│ • Real-time UI  │
│ • Task Planning │    │ • Session Mgmt  │    │ • Input Handler │
│ • Error Handling│    │ • Message Queue │    │ • Error Display │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Message Flow

1. **Agent → Server**: MCP protocol messages (requests, status updates)
2. **Server → CLI**: Real-time message forwarding and session management
3. **CLI → Server**: Human responses and commands
4. **Server → Agent**: Response delivery and session coordination

## 🧪 Testing

### Run All Tests

```bash
# Run complete test suite
npm test

# Run specific package tests
npm test --workspace=@powersteer/server
npm test --workspace=@powersteer/cli
npm test --workspace=@powersteer/common
```

### Test Categories

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Cross-component communication
- **Error Recovery Tests**: Failure scenarios and recovery
- **End-to-End Tests**: Complete workflow validation

## 🚀 Deployment

### Production Server Deployment

```bash
# Build for production
npm run build

# Start production server
NODE_ENV=production npm run start:server

# Or use PM2 for process management
pm2 start ecosystem.config.js
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
EXPOSE 3001
CMD ["npm", "run", "start:server"]
```

### Environment Variables

```bash
# Server Configuration
PORT=3001
HOST=0.0.0.0
NODE_ENV=production

# Security
CORS_ORIGIN=https://your-domain.com
SESSION_SECRET=your-secret-key

# Monitoring
LOG_LEVEL=info
METRICS_ENABLED=true
```

## 🔍 Troubleshooting

### Common Issues

**Connection Refused**
```bash
# Check server status
curl -f http://localhost:3001/health

# Verify WebSocket connection
wscat -c ws://localhost:3001
```

**CLI Not Connecting**
```bash
# Check server URL configuration
echo $POWERSTEER_SERVER_URL

# Test with verbose logging
DEBUG=powersteer:* npm run start:cli
```

**Session Timeouts**
```bash
# Increase session timeout in server config
"session": { "timeout": 600000 }

# Monitor session activity
tail -f logs/powersteer.log | grep session
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Setup

```bash
# Install development dependencies
npm install

# Run in development mode
npm run dev

# Run tests in watch mode
npm run test:watch

# Lint and format code
npm run lint
npm run format
```

## 📖 Documentation

- **[API Reference](./API.md)** - Complete WebSocket API documentation
- **[MCP Setup Guide](./MCP_SETUP.md)** - Model Context Protocol integration with MCP CLI
- **[Deployment Guide](./DEPLOYMENT.md)** - Production deployment instructions
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Common issues and solutions
- **[Contributing](../CONTRIBUTING.md)** - Development guidelines and contribution process

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Model Context Protocol specification
- WebSocket community standards
- TypeScript and Node.js ecosystems
- Contributors and early adopters
