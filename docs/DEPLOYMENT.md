# Powersteer Deployment Guide

This guide covers production deployment strategies for the Powersteer MCP server and CLI system.

## 🏗️ Deployment Architecture

### Recommended Production Setup

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Powersteer    │    │   Monitoring    │
│   (nginx/HAProxy│◄──►│   Server        │◄──►│   (Prometheus)  │
│   SSL/TLS)      │    │   (Node.js)     │    │   <PERSON>ana       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI Agents     │    │   Redis Cache   │    │   Log Storage   │
│   (Multiple)    │    │   (Sessions)    │    │   (ELK Stack)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🐳 Docker Deployment

### Multi-Stage Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
COPY packages/*/package*.json ./packages/*/
RUN npm ci

COPY . .
RUN npm run build
RUN npm prune --production

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S powersteer -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=powersteer:nodejs /app/dist ./dist
COPY --from=builder --chown=powersteer:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=powersteer:nodejs /app/package*.json ./

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

USER powersteer
EXPOSE 3001

CMD ["node", "dist/packages/server/src/server.js"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  powersteer-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - powersteer-network
    volumes:
      - ./logs:/app/logs

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - powersteer-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - powersteer-server
    restart: unless-stopped
    networks:
      - powersteer-network

volumes:
  redis-data:

networks:
  powersteer-network:
    driver: bridge
```

## ☸️ Kubernetes Deployment

### Deployment Manifest

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: powersteer-server
  labels:
    app: powersteer-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: powersteer-server
  template:
    metadata:
      labels:
        app: powersteer-server
    spec:
      containers:
      - name: powersteer-server
        image: powersteer/server:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: powersteer-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: powersteer-service
spec:
  selector:
    app: powersteer-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3001
  type: LoadBalancer
```

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: powersteer-config
data:
  server.json: |
    {
      "port": 3001,
      "host": "0.0.0.0",
      "cors": {
        "origin": ["https://your-domain.com"],
        "credentials": true
      },
      "session": {
        "timeout": 300000,
        "cleanup_interval": 60000
      },
      "websocket": {
        "ping_interval": 30000,
        "pong_timeout": 10000
      },
      "redis": {
        "url": "${REDIS_URL}",
        "keyPrefix": "powersteer:"
      }
    }
```

### Secrets

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: powersteer-secrets
type: Opaque
data:
  redis-url: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=  # base64 encoded
  api-key: eW91ci1hcGkta2V5LWhlcmU=              # base64 encoded
```

## 🔧 Environment Configuration

### Production Environment Variables

```bash
# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Security
CORS_ORIGIN=https://your-domain.com
SESSION_SECRET=your-super-secret-session-key
API_KEY=your-api-key-for-authentication

# Database/Cache
REDIS_URL=redis://redis-server:6379
REDIS_PASSWORD=your-redis-password

# Monitoring
LOG_LEVEL=info
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Performance
MAX_CONNECTIONS=1000
MESSAGE_QUEUE_SIZE=10000
SESSION_TIMEOUT=300000

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/powersteer.crt
SSL_KEY_PATH=/etc/ssl/private/powersteer.key
```

### Configuration Files

**config/production.json**:
```json
{
  "server": {
    "port": 3001,
    "host": "0.0.0.0",
    "ssl": {
      "enabled": true,
      "cert": "/etc/ssl/certs/powersteer.crt",
      "key": "/etc/ssl/private/powersteer.key"
    }
  },
  "websocket": {
    "ping_interval": 30000,
    "pong_timeout": 10000,
    "max_connections": 1000
  },
  "session": {
    "timeout": 300000,
    "cleanup_interval": 60000,
    "store": "redis"
  },
  "redis": {
    "url": "${REDIS_URL}",
    "keyPrefix": "powersteer:",
    "retryDelayOnFailover": 100,
    "maxRetriesPerRequest": 3
  },
  "logging": {
    "level": "info",
    "format": "json",
    "file": "/var/log/powersteer/server.log"
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "port": 9090
    },
    "health_check": {
      "enabled": true,
      "interval": 30000
    }
  }
}
```

## 🔒 Security Hardening

### SSL/TLS Configuration

**nginx.conf**:
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/powersteer.crt;
    ssl_certificate_key /etc/ssl/private/powersteer.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    location / {
        proxy_pass http://powersteer-server:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Firewall Rules

```bash
# UFW configuration
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from 10.0.0.0/8 to any port 3001  # Internal network only
sudo ufw enable
```

### Container Security

```dockerfile
# Security-hardened Dockerfile additions
FROM node:18-alpine

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Remove unnecessary packages
RUN apk del --purge wget curl

# Use non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S powersteer -u 1001 -G nodejs

# Set secure file permissions
COPY --chown=powersteer:nodejs --chmod=755 . .

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/packages/server/src/server.js"]
```

## 📊 Monitoring & Observability

### Prometheus Metrics

```javascript
// metrics.js
const prometheus = require('prom-client');

const metrics = {
  connections: new prometheus.Gauge({
    name: 'powersteer_connections_active',
    help: 'Number of active WebSocket connections'
  }),
  
  messages: new prometheus.Counter({
    name: 'powersteer_messages_total',
    help: 'Total number of messages processed',
    labelNames: ['type', 'status']
  }),
  
  sessionDuration: new prometheus.Histogram({
    name: 'powersteer_session_duration_seconds',
    help: 'Session duration in seconds',
    buckets: [1, 5, 15, 30, 60, 300, 600, 1800]
  }),
  
  responseTime: new prometheus.Histogram({
    name: 'powersteer_response_time_seconds',
    help: 'Response time for human responses',
    buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300]
  })
};

module.exports = metrics;
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "Powersteer Monitoring",
    "panels": [
      {
        "title": "Active Connections",
        "type": "stat",
        "targets": [
          {
            "expr": "powersteer_connections_active",
            "legendFormat": "Connections"
          }
        ]
      },
      {
        "title": "Message Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(powersteer_messages_total[5m])",
            "legendFormat": "Messages/sec"
          }
        ]
      },
      {
        "title": "Response Time Distribution",
        "type": "heatmap",
        "targets": [
          {
            "expr": "powersteer_response_time_seconds_bucket",
            "legendFormat": "Response Time"
          }
        ]
      }
    ]
  }
}
```

## 🚀 Scaling Strategies

### Horizontal Scaling

```yaml
# HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: powersteer-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: powersteer-server
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Load Balancing

```nginx
upstream powersteer_backend {
    least_conn;
    server powersteer-1:3001 max_fails=3 fail_timeout=30s;
    server powersteer-2:3001 max_fails=3 fail_timeout=30s;
    server powersteer-3:3001 max_fails=3 fail_timeout=30s;
}

server {
    location / {
        proxy_pass http://powersteer_backend;
        proxy_set_header Connection "upgrade";
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        
        # WebSocket specific
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
name: Deploy Powersteer

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    - run: npm ci
    - run: npm run test
    - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: docker build -t powersteer/server:${{ github.sha }} .
    
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push powersteer/server:${{ github.sha }}
    
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/powersteer-server powersteer-server=powersteer/server:${{ github.sha }}
        kubectl rollout status deployment/powersteer-server
```

## 🔧 Troubleshooting

### Common Issues

**High Memory Usage**:
```bash
# Monitor memory usage
kubectl top pods
docker stats

# Check for memory leaks
node --inspect dist/packages/server/src/server.js
```

**Connection Issues**:
```bash
# Check WebSocket connectivity
wscat -c wss://your-domain.com

# Verify SSL certificate
openssl s_client -connect your-domain.com:443
```

**Performance Issues**:
```bash
# Monitor metrics
curl http://localhost:9090/metrics

# Check Redis performance
redis-cli --latency-history -i 1
```

### Log Analysis

```bash
# Structured log analysis
kubectl logs -f deployment/powersteer-server | jq '.level == "error"'

# Performance monitoring
tail -f /var/log/powersteer/server.log | grep "response_time"
```
