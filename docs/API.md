# Powersteer API Documentation

This document provides comprehensive API documentation for the Powersteer MCP server and client interfaces.

## 🔌 WebSocket API

### Connection

**Endpoint**: `ws://localhost:3001`

**Connection Headers**:
```javascript
{
  "Sec-WebSocket-Protocol": "mcp-v1",
  "User-Agent": "Powersteer-Client/1.0.0"
}
```

### Message Format

All messages follow the MCP protocol specification with the following structure:

```typescript
interface Message {
  id: string;           // Unique message identifier
  type: MessageType;    // Message type enum
  content: string;      // Message content
  timestamp: Date;      // Message timestamp
  sessionId?: string;   // Optional session identifier
}

type MessageType = 'agent_request' | 'human_response' | 'system';
```

## 📨 Message Types

### Agent Request

Sent by AI agents to request human input or approval.

```typescript
interface AgentRequest extends Message {
  type: 'agent_request';
  context?: {
    code?: string;        // Code snippet for review
    file?: string;        // File path
    branch?: string;      // Git branch
    errors?: string[];    // Error messages
    metadata?: Record<string, any>; // Additional context
  };
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  timeout?: number;       // Request timeout in milliseconds
}
```

**Example**:
```json
{
  "id": "req_123456789",
  "type": "agent_request",
  "content": "Please review this database migration before deployment",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "sessionId": "session_abc123",
  "context": {
    "file": "migrations/001_add_user_table.sql",
    "code": "CREATE TABLE users (id SERIAL PRIMARY KEY, email VARCHAR(255) UNIQUE);",
    "branch": "feature/user-management"
  },
  "priority": "high",
  "timeout": 300000
}
```

### Human Response

Sent by humans in response to agent requests.

```typescript
interface HumanResponse extends Message {
  type: 'human_response';
  requestId: string;      // ID of the original agent request
  action: 'approve' | 'reject' | 'modify' | 'guide';
  modifications?: string; // Suggested changes (for 'modify' action)
  reasoning?: string;     // Explanation for the decision
}
```

**Example**:
```json
{
  "id": "resp_987654321",
  "type": "human_response",
  "content": "Migration approved with minor suggestions",
  "timestamp": "2024-01-15T10:35:00.000Z",
  "sessionId": "session_abc123",
  "requestId": "req_123456789",
  "action": "modify",
  "modifications": "Add NOT NULL constraint to email field",
  "reasoning": "Email should be required for user identification"
}
```

### System Messages

Internal system messages for status updates and notifications.

```typescript
interface SystemMessage extends Message {
  type: 'system';
  category: 'connection' | 'session' | 'error' | 'info';
  level: 'debug' | 'info' | 'warn' | 'error';
}
```

**Example**:
```json
{
  "id": "sys_555666777",
  "type": "system",
  "content": "Session timeout warning: 60 seconds remaining",
  "timestamp": "2024-01-15T10:40:00.000Z",
  "sessionId": "session_abc123",
  "category": "session",
  "level": "warn"
}
```

## 🔄 Connection Lifecycle

### 1. Connection Establishment

```javascript
const ws = new WebSocket('ws://localhost:3001');

ws.on('open', () => {
  console.log('Connected to Powersteer server');
  
  // Send initial handshake
  ws.send(JSON.stringify({
    type: 'handshake',
    client: 'agent',
    version: '1.0.0'
  }));
});
```

### 2. Session Management

```javascript
// Session creation (automatic on first message)
const sessionMessage = {
  id: generateId(),
  type: 'agent_request',
  content: 'Initialize session',
  timestamp: new Date(),
  // sessionId will be assigned by server
};

// Session heartbeat (sent automatically by client)
const heartbeat = {
  type: 'ping',
  timestamp: new Date()
};
```

### 3. Error Handling

```javascript
ws.on('error', (error) => {
  console.error('WebSocket error:', error);
  // Implement exponential backoff reconnection
  scheduleReconnect();
});

ws.on('close', (code, reason) => {
  console.log(`Connection closed: ${code} - ${reason}`);
  if (code !== 1000) { // Not normal closure
    scheduleReconnect();
  }
});
```

## 🛠️ Server API

### Health Check

**GET** `/health`

Returns server health status.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "connections": 5,
  "sessions": 3
}
```

### Metrics

**GET** `/metrics`

Returns server metrics in Prometheus format.

**Response**:
```
# HELP powersteer_connections_total Total number of WebSocket connections
# TYPE powersteer_connections_total counter
powersteer_connections_total 150

# HELP powersteer_sessions_active Number of active sessions
# TYPE powersteer_sessions_active gauge
powersteer_sessions_active 12

# HELP powersteer_messages_total Total number of messages processed
# TYPE powersteer_messages_total counter
powersteer_messages_total{type="agent_request"} 89
powersteer_messages_total{type="human_response"} 76
```

## 🔐 Authentication & Security

### API Key Authentication

For production deployments, include API key in connection headers:

```javascript
const ws = new WebSocket('ws://localhost:3001', {
  headers: {
    'Authorization': 'Bearer your-api-key-here',
    'X-Client-ID': 'your-client-id'
  }
});
```

### Rate Limiting

Default rate limits:
- **Connections**: 100 per IP per minute
- **Messages**: 1000 per session per minute
- **Request Size**: 1MB maximum

### CORS Configuration

```javascript
// Server CORS settings
{
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true,
  methods: ['GET', 'POST'],
  allowedHeaders: ['Authorization', 'Content-Type']
}
```

## 📊 Error Codes

### WebSocket Close Codes

| Code | Description | Action |
|------|-------------|---------|
| 1000 | Normal closure | No action needed |
| 1001 | Going away | Reconnect after delay |
| 1002 | Protocol error | Check message format |
| 1003 | Unsupported data | Verify message type |
| 1006 | Abnormal closure | Implement reconnection |
| 1011 | Server error | Check server logs |
| 4000 | Authentication failed | Verify credentials |
| 4001 | Rate limit exceeded | Implement backoff |
| 4002 | Session expired | Create new session |

### HTTP Status Codes

| Code | Description | Response |
|------|-------------|----------|
| 200 | Success | Request completed |
| 400 | Bad Request | Invalid parameters |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error occurred |
| 503 | Service Unavailable | Server overloaded |

## 🔧 Client Libraries

### Node.js Client

```javascript
const { PowersteerClient } = require('@powersteer/client');

const client = new PowersteerClient({
  url: 'ws://localhost:3001',
  apiKey: 'your-api-key',
  reconnect: true,
  maxReconnectAttempts: 5
});

// Send agent request
await client.sendRequest({
  content: 'Please review this code change',
  context: { file: 'src/app.ts' },
  priority: 'medium'
});

// Listen for responses
client.on('human_response', (response) => {
  console.log('Received response:', response);
});
```

### Python Client

```python
import asyncio
import websockets
import json

class PowersteerClient:
    def __init__(self, url, api_key=None):
        self.url = url
        self.api_key = api_key
        self.ws = None
    
    async def connect(self):
        headers = {}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        self.ws = await websockets.connect(self.url, extra_headers=headers)
    
    async def send_request(self, content, context=None):
        message = {
            'id': str(uuid.uuid4()),
            'type': 'agent_request',
            'content': content,
            'timestamp': datetime.utcnow().isoformat(),
            'context': context or {}
        }
        await self.ws.send(json.dumps(message))
```

## 📈 Performance Considerations

### Message Batching

For high-throughput scenarios, batch multiple messages:

```javascript
const batch = {
  type: 'batch',
  messages: [
    { type: 'agent_request', content: 'Request 1' },
    { type: 'agent_request', content: 'Request 2' },
    { type: 'agent_request', content: 'Request 3' }
  ]
};
```

### Connection Pooling

Maintain connection pools for multiple agents:

```javascript
class ConnectionPool {
  constructor(maxConnections = 10) {
    this.pool = [];
    this.maxConnections = maxConnections;
  }
  
  async getConnection() {
    if (this.pool.length > 0) {
      return this.pool.pop();
    }
    return new PowersteerClient();
  }
  
  releaseConnection(connection) {
    if (this.pool.length < this.maxConnections) {
      this.pool.push(connection);
    } else {
      connection.close();
    }
  }
}
```

## 🔍 Debugging

### Enable Debug Logging

```bash
# Server debugging
DEBUG=powersteer:* npm run start:server

# Client debugging
DEBUG=powersteer:client npm run start:cli
```

### Message Tracing

```javascript
// Enable message tracing
client.on('message', (message) => {
  console.log('Message trace:', {
    id: message.id,
    type: message.type,
    timestamp: message.timestamp,
    size: JSON.stringify(message).length
  });
});
```

### Performance Monitoring

```javascript
// Monitor connection performance
client.on('latency', (latency) => {
  console.log(`Round-trip latency: ${latency}ms`);
});

client.on('throughput', (stats) => {
  console.log(`Messages/sec: ${stats.messagesPerSecond}`);
});
```
