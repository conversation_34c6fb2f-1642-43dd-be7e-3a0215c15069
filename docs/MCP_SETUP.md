# Powersteer MCP Integration Guide

This guide provides comprehensive instructions for setting up <PERSON>tee<PERSON> as an MCP (Model Context Protocol) server for AI agents, while humans use the dedicated Powersteer CLI interface.

## 🎯 Overview

Powersteer implements a dual-interface architecture:

- **🤖 AI Agents** → Connect via **MCP Protocol** → **Powersteer MCP Server**
- **👤 Humans** → Connect via **Terminal Interface** → **Powersteer CLI**

The MCP server and CLI communicate through WebSocket, enabling seamless agent-human interaction through standardized protocols.

## 🏗️ Architecture Overview

```
┌─────────────┐    MCP Protocol    ┌──────────────────┐    WebSocket    ┌─────────────────┐
│             │ ──────────────────► │                  │ ──────────────► │                 │
│  AI Agent   │                    │ Powersteer MCP   │                 │ Powersteer      │
│ (MCP Client)│                    │     Server       │                 │ WebSocket Server│
│             │ ◄────────────────── │                  │ ◄────────────── │                 │
└─────────────┘    MCP Response    └──────────────────┘    WebSocket    └─────────────────┘
                                                                                   │
                                                                                   │ WebSocket
                                                                                   ▼
                                                                        ┌─────────────────┐
                                                                        │                 │
                                                                        │ Powersteer CLI  │
                                                                        │ (Human Interface)│
                                                                        │                 │
                                                                        └─────────────────┘
```

### 🔄 **Interaction Flow:**

1. **AI Agent** sends request via MCP protocol to **Powersteer MCP Server**
2. **MCP Server** forwards request to **WebSocket Server**
3. **Human** sees request in **Powersteer CLI** interface
4. **Human** responds via CLI
5. **Response** flows back: CLI → WebSocket → MCP Server → Agent

### 🎭 **Role Clarification:**

- **MCP CLI Tools** (like `@modelcontextprotocol/cli`) are for **development and testing only**
- **Production agents** use MCP client libraries, not CLI tools
- **Production humans** use the Powersteer CLI, not MCP CLI tools

## 📋 Prerequisites

- Node.js 18+
- Powersteer installed and built
- MCP CLI tools (optional, for testing)

## 🚀 Quick Setup

### 1. Start Powersteer Server

```bash
# Terminal 1: Start the integrated server (WebSocket + MCP together)
npm run start:server
```

This starts both:
- **MCP server** (stdio transport) for AI agents
- **WebSocket server** (port 8080) for human CLI

### 2. Configure MCP Client (mcp.json)

For AI clients like Claude Desktop, add this to your `mcp.json`:

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["packages/server/build/cli.js"],
      "cwd": "/absolute/path/to/your/powersteer3"
    }
  }
}
```

**Alternative using npm:**
```json
{
  "mcpServers": {
    "powersteer": {
      "command": "npm",
      "args": ["run", "start:server"],
      "cwd": "/absolute/path/to/your/powersteer3"
    }
  }
}
```

### 3. Start Human Interface

```bash
# Terminal 3: Start the Powersteer CLI (for humans)
npm run start:cli
```

### 3. Test with Development Tools (Optional)

For development and testing, you can use MCP CLI tools to simulate an AI agent:

```bash
# Install MCP CLI tools for testing (optional)
npm install -g @modelcontextprotocol/cli

# Test MCP server connection
npx @modelcontextprotocol/cli list-tools \
  --server-command "node packages/server/build/mcp/mcp-server.js"

# Expected output:
# Tools available:
# - request_human_input: Request input, confirmation, or decision from a human operator
```

**Note**: MCP CLI tools are for development/testing only. In production, AI agents use MCP client libraries.

## 🔧 Integration Configurations

### VS Code Integration

Add Powersteer to your VS Code MCP settings in `.vscode/settings.json`:

```json
{
  "mcp.servers": {
    "powersteer": {
      "command": "node",
      "args": ["./packages/server/build/mcp/mcp-server.js"],
      "env": {
        "NODE_ENV": "development",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

### Claude Desktop Integration

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["/absolute/path/to/powersteer/packages/server/build/mcp/mcp-server.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

### Cursor IDE Integration

Add to Cursor's MCP configuration:

```json
{
  "mcp.servers": {
    "powersteer": {
      "command": "node",
      "args": ["./packages/server/build/mcp/mcp-server.js"],
      "timeout": 30000
    }
  }
}
```

## 🛠️ Advanced Configuration

### Production MCP Server Configuration

Create `config/mcp-server.json`:

```json
{
  "server": {
    "name": "powersteer-mcp",
    "version": "1.0.0",
    "timeout": 30000,
    "retries": 3
  },
  "transport": {
    "type": "stdio",
    "options": {
      "bufferSize": 8192
    }
  },
  "session": {
    "timeout": 600000,
    "maxSessions": 100,
    "cleanupInterval": 60000
  },
  "logging": {
    "level": "info",
    "format": "json"
  }
}
```

Start with configuration:

```bash
node packages/server/build/cli.js --config config/mcp-server.json --mcp-only
```

### Docker MCP Server

Create `Dockerfile.mcp`:

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY packages/server/build ./packages/server/build
COPY packages/common/build ./packages/common/build

EXPOSE 3001

CMD ["node", "packages/server/build/mcp/mcp-server.js"]
```

Build and run:

```bash
docker build -f Dockerfile.mcp -t powersteer-mcp .
docker run -p 3001:3001 powersteer-mcp
```

## 🧪 Development Testing

### Testing MCP Integration (Development Only)

**Important**: These tests simulate what an AI agent would do. In production, real AI agents use MCP client libraries, not CLI commands.

```bash
# Test request_human_input tool (simulates agent request)
npx @modelcontextprotocol/cli call-tool \
  --server-command "node packages/server/build/mcp/mcp-server.js" \
  --tool request_human_input \
  --args '{
    "agentId": "test-agent-001",
    "type": "confirmation",
    "priority": "normal",
    "summary": "Test MCP integration",
    "details": "Verifying that MCP protocol communication works correctly",
    "timeout": 60
  }'

# This will send a request that appears in the Powersteer CLI for human response
```

### Production AI Agent Example

This shows how a real AI agent would connect to Powersteer:

```javascript
#!/usr/bin/env node
// Example: How an AI agent connects to Powersteer MCP server

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function aiAgentExample() {
  // AI agent creates MCP client
  const transport = new StdioClientTransport({
    command: 'node',
    args: ['packages/server/build/mcp/mcp-server.js']
  });

  const client = new Client({
    name: 'my-ai-coding-agent',
    version: '1.0.0'
  }, {
    capabilities: {}
  });

  try {
    console.log('🤖 AI Agent connecting to Powersteer MCP server...');
    await client.connect(transport);
    console.log('✅ Connected successfully');

    // AI agent requests human input
    console.log('🤖 Requesting human review...');
    const result = await client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'my-ai-coding-agent',
          type: 'feedback',
          priority: 'high',
          summary: 'Please review this security-critical code change',
          details: 'I modified the authentication function to fix a potential vulnerability',
          context: {
            file: 'src/auth.js',
            change: 'Added rate limiting to prevent brute force attacks'
          },
          timeout: 300
        }
      }
    });

    console.log('👤 Human response received:', result);
    // AI agent continues based on human feedback

  } catch (error) {
    console.error('❌ AI agent communication failed:', error);
    process.exit(1);
  } finally {
    await client.close();
  }
}

aiAgentExample();
```

**Note**: The human sees this request in their Powersteer CLI and responds there, not via MCP tools.

## 🔍 Troubleshooting

### Common Issues

1. **Server not starting**
   ```bash
   # Check if port is available
   lsof -i :3001
   
   # Start with debug logging
   DEBUG=powersteer:* node packages/server/build/mcp/mcp-server.js
   ```

2. **MCP client connection timeout**
   ```bash
   # Increase timeout in client configuration
   {
     "timeout": 60000,
     "retries": 5
   }
   ```

3. **Tool not found error**
   ```bash
   # Verify tools are registered
   mcp list-tools --server-command "node packages/server/build/mcp/mcp-server.js"
   ```

### Debug Mode

Enable debug logging:

```bash
# Server-side debugging
DEBUG=powersteer:mcp:* node packages/server/build/mcp/mcp-server.js

# Client-side debugging (if using MCP CLI)
DEBUG=mcp:* mcp list-tools --server-command "node packages/server/build/mcp/mcp-server.js"
```

## 📚 Next Steps

1. **Read the [API Documentation](./API.md)** for detailed protocol specifications
2. **Check [Examples](../examples/basic-usage.md)** for practical usage scenarios  
3. **Review [Deployment Guide](./DEPLOYMENT.md)** for production setup
4. **See [Troubleshooting Guide](./TROUBLESHOOTING.md)** for common issues

## 🤝 Support

- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join GitHub Discussions for questions
- **Documentation**: Check the docs/ directory for more guides
