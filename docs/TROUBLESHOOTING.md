# Powersteer Troubleshooting Guide

This guide helps diagnose and resolve common issues with the Powersteer MCP server and CLI system.

## 🔍 Quick Diagnostics

### Health Check Commands

```bash
# Check server health
curl -f http://localhost:3001/health

# Test WebSocket connection
wscat -c ws://localhost:3001

# Verify CLI connectivity
POWERSTEER_SERVER_URL=ws://localhost:3001 npm run start:cli -- --test-connection

# Check all services
npm run health-check
```

### Log Locations

```bash
# Server logs
tail -f logs/powersteer-server.log

# CLI logs
tail -f ~/.powersteer/cli.log

# System logs (Linux)
journalctl -u powersteer-server -f

# Docker logs
docker logs -f powersteer-server
```

## 🚫 Connection Issues

### Problem: "Connection Refused"

**Symptoms**:
- <PERSON>L<PERSON> shows "Failed to connect to server"
- WebSocket connection errors
- Health check returns connection refused

**Diagnosis**:
```bash
# Check if server is running
ps aux | grep powersteer
netstat -tlnp | grep 3001

# Verify server configuration
cat config/server.json | jq '.port'

# Test local connectivity
telnet localhost 3001
```

**Solutions**:

1. **Start the server**:
   ```bash
   npm run start:server
   # Or with PM2
   pm2 start ecosystem.config.js
   ```

2. **Check port configuration**:
   ```bash
   # Verify port is not in use
   lsof -i :3001
   
   # Use different port if needed
   PORT=3002 npm run start:server
   ```

3. **Firewall issues**:
   ```bash
   # Check firewall rules
   sudo ufw status
   
   # Allow port if needed
   sudo ufw allow 3001/tcp
   ```

### Problem: "WebSocket Connection Failed"

**Symptoms**:
- Connection establishes but immediately closes
- Intermittent connection drops
- SSL/TLS handshake failures

**Diagnosis**:
```bash
# Test WebSocket with verbose output
wscat -c ws://localhost:3001 --verbose

# Check SSL certificate (for wss://)
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Monitor connection attempts
DEBUG=powersteer:websocket npm run start:server
```

**Solutions**:

1. **Protocol mismatch**:
   ```bash
   # Ensure consistent protocol
   # Use ws:// for development, wss:// for production
   export POWERSTEER_SERVER_URL=ws://localhost:3001
   ```

2. **SSL certificate issues**:
   ```bash
   # Verify certificate validity
   openssl x509 -in cert.pem -text -noout
   
   # Update certificate if expired
   certbot renew
   ```

3. **Proxy/Load balancer configuration**:
   ```nginx
   # nginx WebSocket configuration
   location / {
       proxy_pass http://backend;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
   }
   ```

## 🔄 Session Management Issues

### Problem: "Session Timeout"

**Symptoms**:
- Frequent session expiration messages
- Loss of conversation context
- Repeated authentication requests

**Diagnosis**:
```bash
# Check session configuration
cat config/server.json | jq '.session'

# Monitor session activity
grep "session" logs/powersteer-server.log | tail -20

# Check Redis connection (if using Redis for sessions)
redis-cli ping
```

**Solutions**:

1. **Increase session timeout**:
   ```json
   {
     "session": {
       "timeout": 600000,
       "cleanup_interval": 120000
     }
   }
   ```

2. **Fix Redis connectivity**:
   ```bash
   # Test Redis connection
   redis-cli -h redis-host -p 6379 ping
   
   # Check Redis memory usage
   redis-cli info memory
   ```

3. **Session cleanup issues**:
   ```bash
   # Manual session cleanup
   redis-cli --scan --pattern "powersteer:session:*" | xargs redis-cli del
   ```

### Problem: "Session Not Found"

**Symptoms**:
- "Invalid session ID" errors
- Unable to resume conversations
- Session data corruption

**Diagnosis**:
```bash
# List active sessions
redis-cli keys "powersteer:session:*"

# Check session data
redis-cli get "powersteer:session:your-session-id"

# Verify session store configuration
grep -r "session" config/
```

**Solutions**:

1. **Session store corruption**:
   ```bash
   # Clear corrupted sessions
   redis-cli flushdb
   
   # Restart with clean state
   npm run start:server
   ```

2. **Configuration mismatch**:
   ```json
   {
     "session": {
       "store": "redis",
       "redis": {
         "url": "redis://localhost:6379",
         "keyPrefix": "powersteer:"
       }
     }
   }
   ```

## 💾 Memory and Performance Issues

### Problem: "High Memory Usage"

**Symptoms**:
- Server becomes unresponsive
- Out of memory errors
- Slow response times

**Diagnosis**:
```bash
# Monitor memory usage
top -p $(pgrep -f powersteer)
ps aux --sort=-%mem | grep powersteer

# Node.js memory profiling
node --inspect --max-old-space-size=4096 dist/packages/server/src/server.js

# Check for memory leaks
npm run test:memory-leak
```

**Solutions**:

1. **Increase memory limits**:
   ```bash
   # Node.js memory limit
   node --max-old-space-size=4096 dist/packages/server/src/server.js
   
   # Docker memory limit
   docker run -m 2g powersteer/server
   ```

2. **Session cleanup**:
   ```json
   {
     "session": {
       "cleanup_interval": 30000,
       "max_sessions": 1000
     }
   }
   ```

3. **Message queue limits**:
   ```json
   {
     "websocket": {
       "max_message_size": 1048576,
       "message_queue_size": 1000
     }
   }
   ```

### Problem: "Slow Response Times"

**Symptoms**:
- Delayed message delivery
- Timeout errors
- Poor user experience

**Diagnosis**:
```bash
# Monitor response times
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:3001/health

# Check system resources
iostat -x 1
vmstat 1

# Profile application performance
npm run profile
```

**Solutions**:

1. **Database optimization**:
   ```bash
   # Redis performance tuning
   redis-cli config set maxmemory-policy allkeys-lru
   redis-cli config set tcp-keepalive 60
   ```

2. **Connection pooling**:
   ```json
   {
     "websocket": {
       "max_connections": 1000,
       "connection_timeout": 30000
     }
   }
   ```

## 🔐 Authentication Issues

### Problem: "Authentication Failed"

**Symptoms**:
- "Invalid API key" errors
- Unauthorized access attempts
- Authentication bypass

**Diagnosis**:
```bash
# Check API key configuration
grep -r "api.*key" config/

# Verify authentication headers
curl -H "Authorization: Bearer your-api-key" http://localhost:3001/health

# Monitor authentication attempts
grep "auth" logs/powersteer-server.log
```

**Solutions**:

1. **API key validation**:
   ```bash
   # Generate new API key
   openssl rand -hex 32
   
   # Update configuration
   export API_KEY=your-new-api-key
   ```

2. **Authentication middleware**:
   ```javascript
   // Verify authentication setup
   const auth = require('./middleware/auth');
   app.use('/api', auth.validateApiKey);
   ```

## 🔧 CLI-Specific Issues

### Problem: "CLI Interface Broken"

**Symptoms**:
- Garbled text output
- Missing UI elements
- Keyboard input not working

**Diagnosis**:
```bash
# Check terminal compatibility
echo $TERM
tput colors

# Test CLI in different terminals
TERM=xterm-256color npm run start:cli

# Verify dependencies
npm list ink react
```

**Solutions**:

1. **Terminal compatibility**:
   ```bash
   # Use compatible terminal
   export TERM=xterm-256color
   
   # Install terminal dependencies
   sudo apt-get install ncurses-term
   ```

2. **CLI dependencies**:
   ```bash
   # Reinstall CLI dependencies
   cd packages/cli
   npm install
   npm run build
   ```

### Problem: "Input Lag or Freezing"

**Symptoms**:
- Delayed keyboard input
- UI freezes during typing
- Unresponsive interface

**Diagnosis**:
```bash
# Check CPU usage
top -p $(pgrep -f powersteer-cli)

# Monitor event loop lag
DEBUG=powersteer:performance npm run start:cli

# Test with minimal configuration
npm run start:cli -- --minimal
```

**Solutions**:

1. **Performance optimization**:
   ```json
   {
     "ui": {
       "refresh_rate": 100,
       "buffer_size": 1000,
       "debounce_input": 50
     }
   }
   ```

2. **Resource allocation**:
   ```bash
   # Increase process priority
   nice -n -10 npm run start:cli
   ```

## 🐛 Error Recovery

### Automatic Recovery Scripts

```bash
#!/bin/bash
# recovery.sh - Automatic recovery script

echo "Starting Powersteer recovery..."

# Check server health
if ! curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "Server unhealthy, restarting..."
    pm2 restart powersteer-server
    sleep 10
fi

# Clear stuck sessions
redis-cli --scan --pattern "powersteer:session:*" | while read key; do
    ttl=$(redis-cli ttl "$key")
    if [ "$ttl" -eq -1 ]; then
        echo "Clearing stuck session: $key"
        redis-cli del "$key"
    fi
done

# Check disk space
disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 90 ]; then
    echo "Disk space critical, cleaning logs..."
    find logs/ -name "*.log" -mtime +7 -delete
fi

echo "Recovery complete"
```

### Health Monitoring

```javascript
// health-monitor.js
const WebSocket = require('ws');
const { exec } = require('child_process');

class HealthMonitor {
  constructor() {
    this.checks = [];
    this.interval = 30000; // 30 seconds
  }

  addCheck(name, checkFn) {
    this.checks.push({ name, checkFn });
  }

  async runChecks() {
    for (const check of this.checks) {
      try {
        const result = await check.checkFn();
        if (!result.healthy) {
          console.error(`Health check failed: ${check.name}`, result.error);
          await this.recover(check.name, result.error);
        }
      } catch (error) {
        console.error(`Health check error: ${check.name}`, error);
      }
    }
  }

  async recover(checkName, error) {
    switch (checkName) {
      case 'websocket':
        exec('pm2 restart powersteer-server');
        break;
      case 'redis':
        exec('systemctl restart redis');
        break;
      case 'memory':
        exec('pm2 reload powersteer-server');
        break;
    }
  }

  start() {
    setInterval(() => this.runChecks(), this.interval);
  }
}

// Usage
const monitor = new HealthMonitor();

monitor.addCheck('websocket', async () => {
  return new Promise((resolve) => {
    const ws = new WebSocket('ws://localhost:3001');
    ws.on('open', () => {
      ws.close();
      resolve({ healthy: true });
    });
    ws.on('error', (error) => {
      resolve({ healthy: false, error });
    });
  });
});

monitor.start();
```

## 📞 Getting Help

### Debug Information Collection

```bash
#!/bin/bash
# collect-debug-info.sh

echo "Collecting Powersteer debug information..."

# System information
echo "=== System Info ===" > debug-info.txt
uname -a >> debug-info.txt
node --version >> debug-info.txt
npm --version >> debug-info.txt

# Configuration
echo "=== Configuration ===" >> debug-info.txt
cat config/server.json >> debug-info.txt

# Logs
echo "=== Recent Logs ===" >> debug-info.txt
tail -100 logs/powersteer-server.log >> debug-info.txt

# Process information
echo "=== Process Info ===" >> debug-info.txt
ps aux | grep powersteer >> debug-info.txt
netstat -tlnp | grep 3001 >> debug-info.txt

# Health check
echo "=== Health Check ===" >> debug-info.txt
curl -s http://localhost:3001/health >> debug-info.txt

echo "Debug information saved to debug-info.txt"
```

### Support Channels

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check docs/ directory for detailed guides
- **Community Forum**: Ask questions and share solutions
- **Email Support**: <EMAIL> (for enterprise users)

### Before Reporting Issues

1. **Check existing issues**: Search GitHub issues for similar problems
2. **Collect debug information**: Run the debug collection script
3. **Reproduce the issue**: Provide clear steps to reproduce
4. **Include environment details**: OS, Node.js version, configuration
5. **Provide logs**: Include relevant log excerpts (sanitize sensitive data)
