# Powersteer MCP Quick Reference

Quick reference for AI agents using Powersteer MCP server and humans using Powersteer CLI.

## 🎯 Architecture Overview

- **🤖 AI Agents** → Use MCP clients → Connect to Powersteer MCP server
- **👤 Humans** → Use Powersteer CLI → Connect to WebSocket server
- **🔧 MCP CLI tools** → For development/testing only (simulate agents)

## 🚀 Quick Start Commands

### Start Both Servers
```bash
# Terminal 1: Start WebSocket server (for human CLI)
npm run start:server

# Terminal 2: Start MCP server (for AI agents) - Choose one:
npm run start:mcp                           # From project root
node packages/server/build/mcp/mcp-cli.js  # Direct path
npx @powersteer/server powersteer-mcp       # If published to npm

# Terminal 3: Start human interface
npm run start:cli
```

### Development Testing (Optional)
```bash
# Run all integration tests (simulates agent behavior)
./scripts/test-mcp-integration.sh

# Test specific components
./scripts/test-mcp-integration.sh server    # Test server startup
./scripts/test-mcp-integration.sh tools     # Test tools listing
./scripts/test-mcp-integration.sh demo      # Run interactive demo
```

## 🛠️ Development Commands (Simulate AI Agent)

**Important**: These commands simulate what an AI agent would do. In production, AI agents use MCP client libraries.

### List Available Tools (Development)
```bash
# Using npx (simulates agent discovering tools)
npx @modelcontextprotocol/cli list-tools \
  --server-command "node packages/server/build/mcp/mcp-server.js"

# This shows what tools are available to real AI agents
```

### Simulate Agent Request (Development)
```bash
# Simulate basic confirmation request from agent
npx @modelcontextprotocol/cli call-tool \
  --server-command "node packages/server/build/mcp/mcp-server.js" \
  --tool request_human_input \
  --args '{
    "agentId": "test-agent",
    "type": "confirmation",
    "priority": "normal",
    "summary": "Should I proceed with this action?",
    "timeout": 60
  }'

# This request will appear in the Powersteer CLI for human response
```

# Code review request
npx @modelcontextprotocol/cli call-tool \
  --server-command "node packages/server/build/mcp/mcp-server.js" \
  --tool request_human_input \
  --args '{
    "agentId": "code-reviewer",
    "type": "feedback",
    "priority": "high",
    "summary": "Please review this security-critical function",
    "details": "This function handles user authentication",
    "context": {
      "file": "src/auth.js",
      "function": "validateUser",
      "concerns": ["SQL injection", "timing attacks"]
    },
    "timeout": 300
  }'
```

## 🔧 Configuration Examples

### VS Code Settings
```json
{
  "mcp.servers": {
    "powersteer": {
      "command": "node",
      "args": ["./packages/server/build/mcp/mcp-server.js"],
      "env": {
        "NODE_ENV": "development"
      }
    }
  }
}
```

### Claude Desktop Config
```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["/absolute/path/to/powersteer/packages/server/build/mcp/mcp-server.js"]
    }
  }
}
```

## 📝 Tool Parameters

### request_human_input Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `agentId` | string | ✅ | Unique identifier for the requesting agent |
| `type` | enum | ✅ | Request type: `confirmation`, `information`, `decision`, `approval`, `feedback` |
| `priority` | enum | ❌ | Priority level: `low`, `normal`, `high`, `urgent` (default: `normal`) |
| `summary` | string | ✅ | Brief summary of the request (10-1000 chars) |
| `details` | string | ❌ | Detailed description (max 5000 chars) |
| `context` | object | ❌ | Additional context data |
| `timeout` | number | ❌ | Timeout in seconds (30-3600, default: 300) |

### Request Types

- **`confirmation`**: Yes/no decisions
- **`information`**: Informational requests requiring acknowledgment
- **`decision`**: Multiple choice decisions
- **`approval`**: Approval/rejection requests
- **`feedback`**: Requests for detailed feedback or review

### Priority Levels

- **`low`**: Non-urgent, can wait
- **`normal`**: Standard priority (default)
- **`high`**: Important, needs attention soon
- **`urgent`**: Critical, needs immediate attention

## 🧪 Testing & Debugging

### Debug Mode
```bash
# Server debug logging
DEBUG=powersteer:mcp:* node packages/server/build/mcp/mcp-server.js

# Client debug logging
DEBUG=mcp:* npx @modelcontextprotocol/cli list-tools \
  --server-command "node packages/server/build/mcp/mcp-server.js"
```

### Health Check
```bash
# Test server responsiveness
timeout 10 node -e "
const { spawn } = require('child_process');
const server = spawn('node', ['packages/server/build/mcp/mcp-server.js']);
setTimeout(() => {
  server.kill();
  console.log('✅ Server responsive');
}, 2000);
"
```

## 🔗 Production Integration Patterns

### AI Agent Implementation (Production)
```javascript
// How a real AI agent connects to Powersteer
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class MyAIAgent {
  async requestHumanInput(summary, details, context) {
    // AI agent creates MCP client
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['packages/server/build/mcp/mcp-server.js']
    });

    const client = new Client({
      name: 'my-ai-coding-agent',
      version: '1.0.0'
    }, { capabilities: {} });

    await client.connect(transport);

    // AI agent requests human input
    const result = await client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'my-ai-coding-agent',
          type: 'feedback',
          priority: 'high',
          summary: summary,
          details: details,
          context: context,
          timeout: 300
        }
      }
    });

    // Human responds via Powersteer CLI, not MCP tools
    return result;
  }
}
```

### Error Handling
```javascript
try {
  const result = await client.request({
    method: 'tools/call',
    params: { /* ... */ }
  });
} catch (error) {
  if (error.code === 'TIMEOUT') {
    console.log('Human response timed out');
  } else if (error.code === 'CANCELLED') {
    console.log('Request was cancelled');
  } else {
    console.error('MCP error:', error);
  }
}
```

## 🚨 Common Issues

### Server Won't Start
```bash
# Check if port is in use
lsof -i :3001

# Check build files exist
ls -la packages/server/build/mcp/

# Run with debug output
DEBUG=* node packages/server/build/mcp/mcp-server.js
```

### Tool Not Found
```bash
# Verify tools are registered
npx @modelcontextprotocol/cli list-tools \
  --server-command "node packages/server/build/mcp/mcp-server.js"

# Check server logs for errors
DEBUG=powersteer:* node packages/server/build/mcp/mcp-server.js
```

### Connection Timeout
```bash
# Increase timeout
npx @modelcontextprotocol/cli call-tool \
  --timeout 60000 \
  --server-command "node packages/server/build/mcp/mcp-server.js" \
  --tool request_human_input \
  --args '{ /* ... */ }'
```

## 📚 Resources

- **[Full MCP Setup Guide](./MCP_SETUP.md)** - Comprehensive setup instructions
- **[API Documentation](./API.md)** - Complete API reference
- **[Examples](../examples/basic-usage.md)** - Usage examples and patterns
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Detailed troubleshooting guide
