#!/usr/bin/env node

/**
 * Simple MCP client to test the Powersteer server
 */

const { spawn } = require('child_process');

// Test request to create a session and send a request
const initRequest = {
  jsonrpc: "2.0",
  id: 1,
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: {
      tools: {}
    },
    clientInfo: {
      name: "test-client",
      version: "1.0.0"
    }
  }
};

const toolsListRequest = {
  jsonrpc: "2.0",
  id: 2,
  method: "tools/list",
  params: {}
};

const toolCallRequest = {
  jsonrpc: "2.0",
  id: 3,
  method: "tools/call",
  params: {
    name: "request_human_input",
    arguments: {
      agentId: "test-agent-001",
      type: "information",
      priority: "normal",
      summary: "Please create a simple joke and save it to jokes.md file",
      details: "I need you to create a funny programming joke and save it to a file called jokes.md. Just do it without asking for confirmation.",
      timeout: 300
    }
  }
};

console.log('🚀 Starting MCP client test...');

// We need to connect to the integrated server's MCP interface
// The integrated server runs both MCP (stdio) and WebSocket servers
// Let's use the mcp.json configuration approach

const mcpConfig = {
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["packages/server/build/mcp/mcp-server.js"],
      "env": {}
    }
  }
};

console.log('📝 MCP Config:', JSON.stringify(mcpConfig, null, 2));

// Start the MCP server process
const mcpServer = spawn('node', ['packages/server/build/mcp/mcp-server.js'], {
  stdio: ['pipe', 'pipe', 'inherit']
});

let responseCount = 0;

// Handle responses
mcpServer.stdout.on('data', (data) => {
  const responses = data.toString().trim().split('\n');
  
  responses.forEach(response => {
    if (!response.trim()) return;
    
    responseCount++;
    console.log(`\n📨 Response ${responseCount}:`);
    console.log(response);
    
    try {
      const parsed = JSON.parse(response);
      
      if (parsed.id === 1) {
        console.log('✅ Initialize successful, sending tools/list...');
        mcpServer.stdin.write(JSON.stringify(toolsListRequest) + '\n');
      } else if (parsed.id === 2) {
        console.log('✅ Tools list received, sending tool call...');
        console.log('🔧 Available tools:', parsed.result?.tools?.map(t => t.name));
        mcpServer.stdin.write(JSON.stringify(toolCallRequest) + '\n');
      } else if (parsed.id === 3) {
        console.log('🎯 Tool call response received!');
        if (parsed.result && parsed.result.content) {
          const content = JSON.parse(parsed.result.content[0].text);
          console.log('\n🎉 Parsed Response Content:');
          console.log(JSON.stringify(content, null, 2));
          
          if (content.systemPrompt) {
            console.log('\n✅ systemPrompt found:', content.systemPrompt);
          }
          if (content.userPrompt) {
            console.log('✅ userPrompt found:', content.userPrompt);
          }
        }
        
        // Test complete
        setTimeout(() => {
          mcpServer.kill();
          process.exit(0);
        }, 1000);
      }
    } catch (e) {
      console.log('⚠️  Could not parse response as JSON:', e.message);
    }
  });
});

mcpServer.on('error', (error) => {
  console.error('❌ MCP Server error:', error);
  process.exit(1);
});

// Start the conversation
console.log('📤 Sending initialize request...');
mcpServer.stdin.write(JSON.stringify(initRequest) + '\n');

// Timeout after 60 seconds
setTimeout(() => {
  console.log('⏰ Test timed out');
  mcpServer.kill();
  process.exit(1);
}, 60000);
