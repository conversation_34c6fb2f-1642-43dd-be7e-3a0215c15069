{"name": "powersteer", "version": "1.0.0", "description": "MCP server and CLI for human-in-the-loop agent communication", "type": "module", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "eslint packages/*/src/**/*.ts", "format": "prettier --write packages/*/src/**/*.ts", "clean": "npm run clean --workspaces", "dev:server": "npm run dev -w @powersteer/server", "dev:cli": "npm run dev -w @powersteer/cli", "start:server": "npm run start -w @powersteer/server", "start:cli": "npm run start -w @powersteer/cli", "start:mcp": "npm run start:mcp -w @powersteer/server"}, "devDependencies": {"@types/node": "^20.19.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "model-context-protocol", "agent", "cli", "human-in-the-loop"], "author": "Powersteer Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "uuid": "^11.1.0", "ws": "^8.18.2", "yaml": "^2.8.0"}}