#!/usr/bin/env node

/**
 * Test script to simulate an agent making a request to Powersteer MCP server
 */

const { spawn } = require('child_process');

// Test request payload
const testRequest = {
  jsonrpc: "2.0",
  id: 1,
  method: "tools/call",
  params: {
    name: "request_human_input",
    arguments: {
      agentId: "test-agent-001",
      type: "information",
      priority: "normal",
      summary: "Please create a simple joke and save it to jokes.md file",
      details: "I need you to create a funny programming joke and save it to a file called jokes.md. Just do it without asking for confirmation.",
      timeout: 300
    }
  }
};

console.log('Starting MCP server test...');
console.log('Sending request:', JSON.stringify(testRequest, null, 2));

// Start the MCP server process
const mcpServer = spawn('node', ['packages/server/build/mcp/mcp-server.js'], {
  stdio: ['pipe', 'pipe', 'inherit']
});

// Send the test request
mcpServer.stdin.write(JSON.stringify(testRequest) + '\n');

// Handle response
mcpServer.stdout.on('data', (data) => {
  const response = data.toString().trim();
  console.log('\n=== MCP Server Response ===');
  console.log(response);
  
  try {
    const parsed = JSON.parse(response);
    if (parsed.result && parsed.result.content) {
      const content = JSON.parse(parsed.result.content[0].text);
      console.log('\n=== Parsed Response Content ===');
      console.log(JSON.stringify(content, null, 2));
      
      if (content.systemPrompt) {
        console.log('\n✅ systemPrompt found:', content.systemPrompt);
      }
      if (content.userPrompt) {
        console.log('✅ userPrompt found:', content.userPrompt);
      }
    }
  } catch (e) {
    console.log('Could not parse response as JSON');
  }
  
  // Clean exit
  setTimeout(() => {
    mcpServer.kill();
    process.exit(0);
  }, 1000);
});

mcpServer.on('error', (error) => {
  console.error('MCP Server error:', error);
  process.exit(1);
});

// Timeout after 30 seconds
setTimeout(() => {
  console.log('Test timed out');
  mcpServer.kill();
  process.exit(1);
}, 30000);
