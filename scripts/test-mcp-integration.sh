#!/bin/bash

# Powersteer MCP Integration Test Script
# Tests MCP server by simulating AI agent behavior
#
# Architecture being tested:
# - AI Agent (this script) -> MCP Protocol -> Powersteer MCP Server
# - Human responses come via Powersteer CLI (not MCP tools)
# - MCP CLI tools used here simulate what real AI agents do

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MCP_SERVER_CMD="node packages/server/build/mcp/mcp-server.js"
TIMEOUT=30

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Powersteer MCP Integration Test${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_step() {
    echo -e "${YELLOW}➤ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Check if MCP CLI is available
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available"
        exit 1
    fi
    
    # Check if Powersteer server build exists
    if [ ! -f "packages/server/build/mcp/mcp-server.js" ]; then
        print_error "Powersteer MCP server build not found. Run 'npm run build' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

test_server_startup() {
    print_step "Testing MCP server startup..."
    
    # Start server in background and test if it responds
    timeout $TIMEOUT $MCP_SERVER_CMD &
    SERVER_PID=$!
    
    sleep 2
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        print_success "MCP server started successfully"
        kill $SERVER_PID 2>/dev/null || true
    else
        print_error "MCP server failed to start"
        return 1
    fi
}

test_mcp_tools_list() {
    print_step "Testing MCP tools list..."
    
    # Test listing tools using MCP CLI
    if npx @modelcontextprotocol/cli list-tools --server-command "$MCP_SERVER_CMD" --timeout $TIMEOUT; then
        print_success "MCP tools list successful"
    else
        print_error "Failed to list MCP tools"
        return 1
    fi
}

test_request_human_input_tool() {
    print_step "Testing request_human_input tool..."
    
    # Test the main tool with a simple request
    local test_args='{
        "agentId": "test-agent-001",
        "type": "information",
        "priority": "low",
        "summary": "MCP integration test",
        "details": "This is an automated test to verify MCP integration is working correctly",
        "timeout": 30
    }'
    
    echo "Calling request_human_input tool with test data..."
    if timeout $TIMEOUT npx @modelcontextprotocol/cli call-tool \
        --server-command "$MCP_SERVER_CMD" \
        --tool request_human_input \
        --args "$test_args"; then
        print_success "request_human_input tool test successful"
    else
        print_error "request_human_input tool test failed"
        return 1
    fi
}

test_programmatic_client() {
    print_step "Testing programmatic MCP client..."
    
    # Create a temporary test script
    cat > /tmp/mcp-test.mjs << 'EOF'
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function test() {
    const transport = new StdioClientTransport({
        command: 'node',
        args: ['packages/server/build/mcp/mcp-server.js']
    });

    const client = new Client({
        name: 'test-client',
        version: '1.0.0'
    }, { capabilities: {} });

    try {
        await client.connect(transport);
        
        const tools = await client.request({
            method: 'tools/list',
            params: {}
        });
        
        console.log('Available tools:', tools.tools.length);
        
        const result = await client.request({
            method: 'tools/call',
            params: {
                name: 'request_human_input',
                arguments: {
                    agentId: 'programmatic-test',
                    type: 'information',
                    priority: 'low',
                    summary: 'Programmatic client test',
                    timeout: 10
                }
            }
        });
        
        console.log('Tool call result:', result.content?.[0]?.text || 'Success');
        
    } finally {
        await client.close();
    }
}

test().catch(console.error);
EOF
    
    if timeout $TIMEOUT node /tmp/mcp-test.mjs; then
        print_success "Programmatic client test successful"
    else
        print_error "Programmatic client test failed"
        return 1
    fi
    
    rm -f /tmp/mcp-test.mjs
}

run_interactive_demo() {
    print_step "Running interactive demo..."
    
    echo "Starting interactive MCP demo..."
    echo "Make sure to start the Powersteer CLI in another terminal:"
    echo "  npm run start:cli"
    echo ""
    echo "Press Enter to continue or Ctrl+C to skip..."
    read -r
    
    if node examples/mcp-cli-demo.js; then
        print_success "Interactive demo completed"
    else
        print_error "Interactive demo failed"
        return 1
    fi
}

show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  all          Run all tests (default)"
    echo "  prereq       Check prerequisites only"
    echo "  server       Test server startup only"
    echo "  tools        Test tools listing only"
    echo "  tool-call    Test tool calling only"
    echo "  client       Test programmatic client only"
    echo "  demo         Run interactive demo only"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Run all tests"
    echo "  $0 server    # Test server startup only"
    echo "  $0 demo      # Run interactive demo"
}

main() {
    local command=${1:-all}
    
    case $command in
        "prereq")
            print_header
            check_prerequisites
            ;;
        "server")
            print_header
            check_prerequisites
            test_server_startup
            ;;
        "tools")
            print_header
            check_prerequisites
            test_mcp_tools_list
            ;;
        "tool-call")
            print_header
            check_prerequisites
            test_request_human_input_tool
            ;;
        "client")
            print_header
            check_prerequisites
            test_programmatic_client
            ;;
        "demo")
            print_header
            check_prerequisites
            run_interactive_demo
            ;;
        "all")
            print_header
            check_prerequisites
            test_server_startup
            test_mcp_tools_list
            test_request_human_input_tool
            test_programmatic_client
            
            echo ""
            echo -e "${GREEN}🎉 All MCP integration tests passed!${NC}"
            echo ""
            echo "Optional: Run interactive demo with:"
            echo "  $0 demo"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Make sure we're in the right directory
if [ ! -f "package.json" ] || ! grep -q "powersteer" package.json; then
    print_error "Please run this script from the Powersteer project root directory"
    exit 1
fi

main "$@"
