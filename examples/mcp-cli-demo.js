#!/usr/bin/env node

/**
 * Powersteer MCP Integration Demo
 *
 * This script demonstrates how AI agents connect to Powersteer via MCP protocol.
 * It simulates what a real AI agent would do when requesting human input.
 *
 * Architecture:
 * - This script acts as an AI agent (using MCP client)
 * - Humans respond via Powersteer CLI (not MCP tools)
 * - MCP CLI tools are used here for demonstration only
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

class PowersteerMCPDemo {
  constructor() {
    this.client = null;
    this.transport = null;
    this.serverProcess = null;
  }

  /**
   * Start the Powersteer MCP server
   */
  async startServer() {
    console.log('🚀 Starting Powersteer MCP server...');
    
    this.serverProcess = spawn('node', [
      'packages/server/build/mcp/mcp-server.js'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Wait for server to initialize
    await setTimeout(2000);
    
    if (this.serverProcess.killed) {
      throw new Error('Failed to start MCP server');
    }
    
    console.log('✅ MCP server started');
  }

  /**
   * Connect MCP client to Powersteer server
   */
  async connectClient() {
    console.log('🔌 Connecting MCP client...');
    
    this.transport = new StdioClientTransport({
      command: 'node',
      args: ['packages/server/build/mcp/mcp-server.js']
    });

    this.client = new Client({
      name: 'powersteer-demo-client',
      version: '1.0.0'
    }, {
      capabilities: {}
    });

    await this.client.connect(this.transport);
    console.log('✅ MCP client connected');
  }

  /**
   * List available tools
   */
  async listTools() {
    console.log('\n📋 Listing available MCP tools...');
    
    const response = await this.client.request({
      method: 'tools/list',
      params: {}
    });

    console.log('Available tools:');
    response.tools.forEach(tool => {
      console.log(`  • ${tool.name}: ${tool.description}`);
    });
    
    return response.tools;
  }

  /**
   * Demo: Code review request
   */
  async demoCodeReview() {
    console.log('\n🔍 Demo: Code Review Request');
    console.log('Sending code review request to human operator...');
    
    const result = await this.client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'demo-coding-agent',
          type: 'feedback',
          priority: 'normal',
          summary: 'Please review this authentication function',
          details: 'I need feedback on the security and performance of this login function',
          context: {
            file: 'src/auth/login.js',
            function: 'authenticateUser',
            code: `
async function authenticateUser(username, password) {
  const user = await db.users.findOne({ username });
  if (!user) return null;
  
  const isValid = await bcrypt.compare(password, user.passwordHash);
  if (!isValid) return null;
  
  return {
    id: user.id,
    username: user.username,
    role: user.role
  };
}`,
            concerns: ['timing attacks', 'error handling', 'rate limiting']
          },
          timeout: 120
        }
      }
    });

    console.log('📝 Human response received:');
    console.log(JSON.stringify(result, null, 2));
  }

  /**
   * Demo: Database migration approval
   */
  async demoDatabaseMigration() {
    console.log('\n🗄️ Demo: Database Migration Approval');
    console.log('Requesting approval for database changes...');
    
    const result = await this.client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'demo-db-agent',
          type: 'approval',
          priority: 'high',
          summary: 'Approve database migration: Add user_sessions table',
          details: 'This migration will create a new table for tracking user sessions',
          context: {
            migration: `
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);`,
            impact: 'Low - New table, no existing data affected',
            rollback: 'DROP TABLE user_sessions CASCADE;',
            estimated_time: '30 seconds'
          },
          timeout: 300
        }
      }
    });

    console.log('✅ Migration approval response:');
    console.log(JSON.stringify(result, null, 2));
  }

  /**
   * Demo: Error resolution guidance
   */
  async demoErrorResolution() {
    console.log('\n❌ Demo: Error Resolution Guidance');
    console.log('Requesting help with build errors...');
    
    const result = await this.client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'demo-build-agent',
          type: 'decision',
          priority: 'urgent',
          summary: 'Build failed with TypeScript errors - how to proceed?',
          details: 'Multiple TypeScript compilation errors are blocking deployment',
          context: {
            errors: [
              "Property 'id' is missing in type 'User' but required in type 'UserWithId'",
              "Cannot find module '@/utils/helper' or its corresponding type declarations",
              "Type 'string | undefined' is not assignable to type 'string'"
            ],
            branch: 'feature/user-management',
            deadline: '2 hours',
            options: [
              'Fix errors manually (estimated 1 hour)',
              'Revert to previous working version',
              'Deploy with --skipLibCheck flag (risky)'
            ]
          },
          timeout: 180
        }
      }
    });

    console.log('🔧 Error resolution guidance:');
    console.log(JSON.stringify(result, null, 2));
  }

  /**
   * Demo: Performance optimization decision
   */
  async demoPerformanceOptimization() {
    console.log('\n⚡ Demo: Performance Optimization Decision');
    console.log('Requesting decision on performance optimization...');
    
    const result = await this.client.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'demo-perf-agent',
          type: 'decision',
          priority: 'normal',
          summary: 'Should I implement caching for user profile queries?',
          details: 'Database queries for user profiles are taking 200ms average. Caching could reduce this to 5ms.',
          context: {
            current_performance: {
              avg_query_time: '200ms',
              queries_per_minute: 1500,
              database_load: '65%'
            },
            proposed_solution: {
              cache_type: 'Redis',
              estimated_improvement: '95% faster queries',
              memory_usage: '+50MB',
              complexity: 'Medium'
            },
            alternatives: [
              'Database query optimization',
              'Connection pooling improvements',
              'Lazy loading implementation'
            ]
          },
          timeout: 240
        }
      }
    });

    console.log('📊 Performance optimization decision:');
    console.log(JSON.stringify(result, null, 2));
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.client) {
      await this.client.close();
    }
    
    if (this.serverProcess && !this.serverProcess.killed) {
      this.serverProcess.kill();
    }
    
    console.log('✅ Cleanup complete');
  }

  /**
   * Run all demos
   */
  async runDemo() {
    try {
      await this.connectClient();
      await this.listTools();
      
      console.log('\n🎭 Running interactive demos...');
      console.log('Note: These demos will wait for human responses in the CLI');
      
      await this.demoCodeReview();
      await setTimeout(2000); // Brief pause between demos
      
      await this.demoDatabaseMigration();
      await setTimeout(2000);
      
      await this.demoErrorResolution();
      await setTimeout(2000);
      
      await this.demoPerformanceOptimization();
      
      console.log('\n🎉 All demos completed successfully!');
      
    } catch (error) {
      console.error('❌ Demo failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

/**
 * Main execution
 */
async function main() {
  const demo = new PowersteerMCPDemo();
  
  console.log('🎯 Powersteer MCP CLI Integration Demo');
  console.log('=====================================');
  console.log('This demo shows how to use Powersteer with MCP protocol');
  console.log('Make sure to start the Powersteer CLI in another terminal:');
  console.log('  npm run start:cli');
  console.log('');
  
  try {
    await demo.runDemo();
  } catch (error) {
    console.error('Demo execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { PowersteerMCPDemo };
