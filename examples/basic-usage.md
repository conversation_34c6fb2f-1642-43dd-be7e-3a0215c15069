wai# Powersteer Basic Usage Examples

This document provides practical examples of using Powersteer for human-in-the-loop agent communication.

## 🚀 Quick Start Example

### 1. Start the System

```bash
# Terminal 1: Start the WebSocket server (for human CLI)
npm run start:server

# Terminal 2: Start the MCP server (for AI agents)
node packages/server/build/mcp/mcp-server.js

# Terminal 3: Start the CLI interface (for humans)
npm run start:cli
```

## 🏗️ Architecture Overview

Powersteer uses a dual-interface architecture:

- **🤖 AI Agents** → Connect via **MCP Protocol** → **Powersteer MCP Server**
- **👤 Humans** → Connect via **Terminal Interface** → **Powersteer CLI**

The MCP server and CLI communicate through WebSocket to enable agent-human interaction.

## 🔧 MCP Integration for AI Agents

### Setting up AI Agents with Powersteer

AI agents use MCP client libraries to connect to Powersteer's MCP server. The MCP CLI tools mentioned below are for development and testing only.

#### 1. Install MCP CLI Tools (Development/Testing Only)

**Important**: These tools are for development and testing. In production, AI agents use MCP client libraries.

```bash
# Install the official MCP CLI (for testing)
npm install -g @modelcontextprotocol/cli

# Or using npx (no global install needed)
npx @modelcontextprotocol/cli --help
```

#### 2. Configure Powersteer as MCP Server

Create an MCP configuration file `mcp-config.json`:

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["./packages/server/build/cli.js"],
      "env": {
        "PORT": "3001",
        "NODE_ENV": "production"
      }
    }
  }
}
```

#### 3. Alternative: Direct MCP Server Setup

You can also run Powersteer as a standalone MCP server:

```bash
# Start Powersteer MCP server on stdio transport
node packages/server/build/mcp/mcp-server.js

# Or with custom configuration
MCP_TRANSPORT=stdio node packages/server/build/cli.js --mcp-only
```

#### 4. Connect MCP CLI to Powersteer

```bash
# List available tools from Powersteer
mcp list-tools --server powersteer

# Use the request_human_input tool
mcp call-tool --server powersteer --tool request_human_input --args '{
  "agentId": "my-coding-agent",
  "type": "confirmation",
  "priority": "high",
  "summary": "Should I proceed with database migration?",
  "details": "This will add a new user_preferences table with JSONB column",
  "context": {
    "migration": "CREATE TABLE user_preferences (id SERIAL, user_id INT, preferences JSONB)",
    "impact": "Low risk - new table only"
  },
  "timeout": 300
}'
```

#### 5. VS Code Integration

Add Powersteer to your VS Code MCP configuration in `settings.json`:

```json
{
  "mcp.servers": {
    "powersteer": {
      "command": "node",
      "args": ["./node_modules/@powersteer/server/build/cli.js"],
      "env": {
        "MCP_TRANSPORT": "stdio"
      }
    }
  }
}
```

#### 6. Claude Desktop Integration

Add to Claude Desktop's MCP configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": ["/path/to/powersteer/packages/server/build/cli.js"],
      "env": {
        "MCP_TRANSPORT": "stdio",
        "PORT": "3001"
      }
    }
  }
}
```

#### 7. Testing MCP Integration

Test your MCP setup with these commands:

```bash
# Test server connection
mcp ping --server powersteer

# List available tools
mcp list-tools --server powersteer
# Expected output:
# - request_human_input: Request input, confirmation, or decision from a human operator

# Test the human input tool
mcp call-tool --server powersteer --tool request_human_input --args '{
  "agentId": "test-agent",
  "type": "confirmation",
  "priority": "normal",
  "summary": "Test human input request",
  "details": "This is a test to verify MCP integration is working",
  "timeout": 60
}'
```

#### 8. Advanced MCP Configuration

For production use, create a more robust configuration:

```json
{
  "mcpServers": {
    "powersteer": {
      "command": "node",
      "args": [
        "./packages/server/build/cli.js",
        "--session-timeout", "600",
        "--max-sessions", "50",
        "--log-level", "info"
      ],
      "env": {
        "NODE_ENV": "production",
        "MCP_TRANSPORT": "stdio",
        "PORT": "3001",
        "POWERSTEER_LOG_LEVEL": "info"
      },
      "timeout": 30000,
      "retries": 3
    }
  }
}
```

#### 9. Programmatic MCP Usage

Use Powersteer from your own MCP client:

```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

// Create MCP client
const transport = new StdioClientTransport({
  command: 'node',
  args: ['./packages/server/build/cli.js']
});

const client = new Client({
  name: 'my-agent',
  version: '1.0.0'
}, {
  capabilities: {}
});

// Connect to Powersteer MCP server
await client.connect(transport);

// Request human input
const result = await client.request({
  method: 'tools/call',
  params: {
    name: 'request_human_input',
    arguments: {
      agentId: 'my-coding-agent',
      type: 'decision',
      priority: 'high',
      summary: 'Should I refactor this legacy code?',
      details: 'The current implementation has technical debt but works',
      context: {
        file: 'src/legacy/old-module.js',
        complexity: 'high',
        test_coverage: '45%'
      },
      timeout: 300
    }
  }
});

console.log('Human response:', result);
```

## 🤖 Production AI Agent Usage

### Real AI Agent Implementation

In production, AI agents use MCP client libraries to connect to Powersteer:

```typescript
// Example: AI coding agent requesting human review
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class AICodingAgent {
  private mcpClient: Client;

  async initialize() {
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['packages/server/build/mcp/mcp-server.js']
    });

    this.mcpClient = new Client({
      name: 'ai-coding-agent',
      version: '1.0.0'
    }, { capabilities: {} });

    await this.mcpClient.connect(transport);
  }

  async requestCodeReview(code: string, file: string) {
    const result = await this.mcpClient.request({
      method: 'tools/call',
      params: {
        name: 'request_human_input',
        arguments: {
          agentId: 'ai-coding-agent',
          type: 'feedback',
          priority: 'high',
          summary: 'Please review this code for security issues',
          details: 'I need human expertise to validate this security-critical code',
          context: {
            file: file,
            code: code,
            concerns: ['input validation', 'authentication', 'authorization']
          },
          timeout: 300
        }
      }
    });

    // Human responds via Powersteer CLI
    return result;
  }
}
```

### 2. Basic Agent-Human Interaction

**Agent sends a request** (via MCP protocol):
```typescript
import { PowersteerClient } from '@powersteer/client';

const client = new PowersteerClient('ws://localhost:3001');

await client.sendRequest({
  content: 'Should I proceed with deleting the old user data?',
  context: {
    action: 'data_cleanup',
    affected_records: 1500,
    backup_status: 'completed'
  },
  priority: 'high'
});
```

**Human sees in CLI**:
```
🤖 Agent Request (HIGH PRIORITY)
┌─────────────────────────────────────────────────────────────┐
│ Should I proceed with deleting the old user data?          │
│                                                             │
│ 📋 Context:                                                 │
│ • Action: data_cleanup                                      │
│ • Affected records: 1,500                                   │
│ • Backup status: completed                                  │
└─────────────────────────────────────────────────────────────┘

Your response: [APPROVE] Yes, proceed with deletion
```

**Agent receives response**:
```typescript
client.on('human_response', (response) => {
  console.log('Human decision:', response.action); // 'approve'
  console.log('Response:', response.content); // 'Yes, proceed with deletion'
  
  // Continue with the approved action
  if (response.action === 'approve') {
    deleteOldUserData();
  }
});
```

## 📝 Code Review Workflow

### Agent Request for Code Review

```typescript
const codeReviewRequest = {
  content: 'Please review this authentication function for security issues',
  context: {
    file: 'src/auth/login.ts',
    code: `
      async function authenticateUser(email: string, password: string) {
        const user = await User.findOne({ email });
        if (user && user.password === password) {
          return generateToken(user);
        }
        throw new Error('Invalid credentials');
      }
    `,
    concerns: [
      'Password comparison might be vulnerable to timing attacks',
      'No rate limiting implemented',
      'Error message reveals user existence'
    ]
  },
  priority: 'medium'
};

await client.sendRequest(codeReviewRequest);
```

### Human Review Response

**CLI Display**:
```
🔍 Code Review Request
┌─────────────────────────────────────────────────────────────┐
│ Please review this authentication function for security     │
│ issues                                                      │
│                                                             │
│ 📁 File: src/auth/login.ts                                  │
│                                                             │
│ 💻 Code:                                                    │
│ async function authenticateUser(email: string, password:    │
│ string) {                                                   │
│   const user = await User.findOne({ email });              │
│   if (user && user.password === password) {                │
│     return generateToken(user);                             │
│   }                                                         │
│   throw new Error('Invalid credentials');                  │
│ }                                                           │
│                                                             │
│ ⚠️  Concerns:                                               │
│ • Password comparison might be vulnerable to timing attacks │
│ • No rate limiting implemented                              │
│ • Error message reveals user existence                      │
└─────────────────────────────────────────────────────────────┘

Your response: [REJECT] Security issues found - see suggestions below

Detailed feedback:
1. Use bcrypt.compare() for secure password comparison
2. Implement rate limiting with express-rate-limit
3. Use generic error message for both cases
4. Add input validation and sanitization
```

### Agent Implements Feedback

```typescript
client.on('human_response', async (response) => {
  if (response.action === 'reject') {
    console.log('Code review failed, implementing feedback...');
    
    // Parse feedback and implement changes
    const improvedCode = `
      async function authenticateUser(email: string, password: string) {
        // Input validation
        if (!email || !password) {
          throw new Error('Authentication failed');
        }
        
        const user = await User.findOne({ email });
        
        // Secure password comparison
        const isValid = user ? await bcrypt.compare(password, user.hashedPassword) : false;
        
        if (isValid) {
          return generateToken(user);
        }
        
        // Generic error message
        throw new Error('Authentication failed');
      }
    `;
    
    // Request another review
    await client.sendRequest({
      content: 'Please review the updated authentication function',
      context: {
        file: 'src/auth/login.ts',
        code: improvedCode,
        changes: 'Implemented security feedback from previous review'
      }
    });
  }
});
```

## 🗄️ Database Migration Approval

### Migration Request

```typescript
const migrationRequest = {
  content: 'Approve database migration: Add user preferences table',
  context: {
    migration_file: '20240115_add_user_preferences.sql',
    sql: `
      CREATE TABLE user_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        preferences JSONB NOT NULL DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
    `,
    impact_analysis: {
      tables_affected: ['user_preferences'],
      estimated_downtime: '< 1 minute',
      rollback_plan: 'DROP TABLE user_preferences;',
      data_loss_risk: 'None - new table'
    }
  },
  priority: 'medium'
};

await client.sendRequest(migrationRequest);
```

### Human Approval Process

**CLI Display**:
```
🗄️  Database Migration Request
┌─────────────────────────────────────────────────────────────┐
│ Approve database migration: Add user preferences table     │
│                                                             │
│ 📄 Migration: 20240115_add_user_preferences.sql            │
│                                                             │
│ 📊 Impact Analysis:                                         │
│ • Tables affected: user_preferences                         │
│ • Estimated downtime: < 1 minute                           │
│ • Data loss risk: None - new table                         │
│ • Rollback plan: DROP TABLE user_preferences;              │
│                                                             │
│ 💻 SQL:                                                     │
│ CREATE TABLE user_preferences (                            │
│   id SERIAL PRIMARY KEY,                                   │
│   user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, │
│   preferences JSONB NOT NULL DEFAULT '{}',                 │
│   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          │
│   updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           │
│ );                                                          │
│                                                             │
│ CREATE INDEX idx_user_preferences_user_id ON               │
│ user_preferences(user_id);                                  │
└─────────────────────────────────────────────────────────────┘

Your response: [APPROVE] Migration looks good, proceed with deployment

Additional notes: Schedule during low-traffic window (2-4 AM UTC)
```

## 🚨 Error Resolution Guidance

### Error Scenario

```typescript
const errorRequest = {
  content: 'Build failed with multiple TypeScript errors. Need guidance on resolution approach.',
  context: {
    build_command: 'npm run build',
    errors: [
      {
        file: 'src/types/user.ts',
        line: 15,
        message: "Property 'id' is missing in type 'CreateUserRequest' but required in type 'User'"
      },
      {
        file: 'src/services/auth.ts',
        line: 42,
        message: "Cannot find module '@/utils/crypto' or its corresponding type declarations"
      },
      {
        file: 'src/components/UserProfile.tsx',
        line: 28,
        message: "Type 'string | undefined' is not assignable to type 'string'"
      }
    ],
    branch: 'feature/user-management',
    last_working_commit: 'abc123def',
    environment: 'development'
  },
  priority: 'high'
};

await client.sendRequest(errorRequest);
```

### Human Guidance Response

**CLI Display**:
```
❌ Build Error - Guidance Needed
┌─────────────────────────────────────────────────────────────┐
│ Build failed with multiple TypeScript errors. Need         │
│ guidance on resolution approach.                            │
│                                                             │
│ 🌿 Branch: feature/user-management                          │
│ 📝 Command: npm run build                                   │
│ 🔄 Last working: abc123def                                  │
│                                                             │
│ 🔧 Errors:                                                  │
│ 1. src/types/user.ts:15                                     │
│    Property 'id' missing in CreateUserRequest              │
│                                                             │
│ 2. src/services/auth.ts:42                                  │
│    Cannot find module '@/utils/crypto'                     │
│                                                             │
│ 3. src/components/UserProfile.tsx:28                        │
│    Type 'string | undefined' not assignable to 'string'    │
└─────────────────────────────────────────────────────────────┘

Your response: [GUIDE] Follow this step-by-step resolution plan:

Resolution Plan:
1. Fix type definitions first:
   - Update CreateUserRequest to omit 'id' field
   - Use Omit<User, 'id' | 'createdAt'> for create requests

2. Fix import path:
   - Check if @/utils/crypto exists or create it
   - Update tsconfig.json paths if needed

3. Handle optional types:
   - Add null checks or use optional chaining
   - Consider using default values

4. Test after each fix:
   - Run 'npm run type-check' between fixes
   - Commit working state before next fix

Priority: Fix in order listed above
```

### Agent Implements Guidance

```typescript
client.on('human_response', async (response) => {
  if (response.action === 'guide') {
    console.log('Received guidance, implementing step by step...');
    
    // Step 1: Fix type definitions
    await updateTypeDefinitions();
    await runTypeCheck();
    
    // Step 2: Fix import paths
    await fixImportPaths();
    await runTypeCheck();
    
    // Step 3: Handle optional types
    await addNullChecks();
    await runTypeCheck();
    
    // Report back success
    await client.sendRequest({
      content: 'All TypeScript errors resolved successfully',
      context: {
        resolution_time: '15 minutes',
        steps_completed: 3,
        build_status: 'passing'
      }
    });
  }
});
```

## 🔄 Continuous Integration Integration

### CI Pipeline Integration

```typescript
// ci-integration.ts
import { PowersteerClient } from '@powersteer/client';

class CIIntegration {
  private client: PowersteerClient;
  
  constructor() {
    this.client = new PowersteerClient(process.env.POWERSTEER_URL);
  }
  
  async requestDeploymentApproval(deploymentInfo: DeploymentInfo) {
    const request = {
      content: `Approve deployment to ${deploymentInfo.environment}`,
      context: {
        environment: deploymentInfo.environment,
        version: deploymentInfo.version,
        changes: deploymentInfo.changes,
        tests_passed: deploymentInfo.testsStatus,
        security_scan: deploymentInfo.securityScan
      },
      priority: deploymentInfo.environment === 'production' ? 'high' : 'medium'
    };
    
    const response = await this.client.sendRequestAndWait(request, 300000); // 5 min timeout
    
    if (response.action === 'approve') {
      console.log('Deployment approved, proceeding...');
      return true;
    } else {
      console.log('Deployment rejected:', response.content);
      return false;
    }
  }
}

// Usage in CI pipeline
const ci = new CIIntegration();
const approved = await ci.requestDeploymentApproval({
  environment: 'production',
  version: '1.2.3',
  changes: ['Fix user authentication bug', 'Add new dashboard feature'],
  testsStatus: 'all passed',
  securityScan: 'no vulnerabilities found'
});

if (approved) {
  // Proceed with deployment
  await deployToProduction();
} else {
  // Stop deployment
  process.exit(1);
}
```

## 📊 Monitoring and Alerts

### Alert Handling

```typescript
const alertRequest = {
  content: 'High CPU usage detected on production server',
  context: {
    alert_type: 'performance',
    severity: 'warning',
    metrics: {
      cpu_usage: '85%',
      memory_usage: '72%',
      response_time: '2.3s',
      error_rate: '0.5%'
    },
    server: 'prod-web-01',
    duration: '10 minutes',
    suggested_actions: [
      'Scale up server resources',
      'Investigate slow queries',
      'Check for memory leaks'
    ]
  },
  priority: 'urgent'
};

await client.sendRequest(alertRequest);
```

**Human Response**:
```
🚨 Production Alert
┌─────────────────────────────────────────────────────────────┐
│ High CPU usage detected on production server               │
│                                                             │
│ 🖥️  Server: prod-web-01                                     │
│ ⏱️  Duration: 10 minutes                                    │
│ 📊 Metrics:                                                 │
│ • CPU: 85% (HIGH)                                           │
│ • Memory: 72%                                               │
│ • Response time: 2.3s                                       │
│ • Error rate: 0.5%                                          │
│                                                             │
│ 💡 Suggested Actions:                                       │
│ • Scale up server resources                                 │
│ • Investigate slow queries                                  │
│ • Check for memory leaks                                    │
└─────────────────────────────────────────────────────────────┘

Your response: [APPROVE] Scale up immediately and investigate

Action plan:
1. Auto-scale to 3 instances immediately
2. Enable detailed performance monitoring
3. Schedule investigation for slow queries tomorrow
4. Set up memory leak detection
```

These examples demonstrate the flexibility and power of Powersteer for various human-in-the-loop scenarios in software development and operations.
